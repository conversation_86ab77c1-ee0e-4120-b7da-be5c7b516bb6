
/* Table of Contents
==================================================
    #Base 960 Grid
    #Tablet (Portrait)
    #Mobile (Portrait)
    #Mobile (Landscape)
    #Clearing */
/* #Base 960 Grid
================================================== */
html {
	min-width: 910px;
}

html.responsive {
	min-width: 0px;
}

.boxed#top {
	margin: 0 auto;
	overflow: visible; /* position:relative; z-index: 3; */
}

.container {
	position: relative;
	width: 100%;
	margin: 0 auto;
	padding: 0px 50px;
	clear: both;
}

.inner-container{
	position: relative;
	height:100%;
	width:100%;
}

/*no z-index for container_wrap or fixed bgs start disapearing when other elements transition -> weird chrome bug*/
.container_wrap {
	clear: both;
	position: relative;
	/* z-index: 1; */
	border-top-style: solid;
	border-top-width: 1px;
}

.unit, .units {
	float: left;
	display: inline;
	margin-left: 50px;
	position: relative;
	z-index: 1;
	min-height: 1px;
}
.row {
	position: relative;
	margin-bottom: 20px;
	clear: both;
}


#wrap_all {
	width: 100%;
	position: static; /*fixes chrome 40 issue with fixed section bgs*/
	z-index: 2;
	overflow: hidden;
}

.boxed #wrap_all {
	overflow: visible; /*needed for cart icon */
}


/* Nested Column Classes */
body .unit.alpha,
body .units.alpha,
body div .first {
	margin-left: 0;
	clear: left;
}
body .unit.alpha, body .units.alpha {
	width: 100%;
}

/* Base sizes */
.container .av-content-full.units  {
	width: 100%
}

.container .av-content-small.units {
	width: 73%
}

.boxed#top, .html_boxed.html_header_sticky #header{
	width: 1010px;
}

.container{
	max-width: 1010px;
}


/* #Tablet (Portrait)
================================================== */
/* Note: Design for a width of 768px, Gutter: 30px, Unit: 32px */
@media only screen and (min-width: 768px) and (max-width: 989px)
{
	.responsive #top {
		overflow-x: hidden;
	}

	.responsive .boxed#top,
	.responsive.html_boxed.html_header_sticky #header{
		width: 782px;
	}

	.responsive .container{
		max-width: 782px;
	}

	.responsive.html_mobile_menu_tablet .boxed #wrap_all {
		overflow: hidden; /*needed for mobile menu scrolling */
	}
}



/* All Tablets and smaller */
@media only screen and (max-width: 989px)
{
	.responsive #top #wrap_all .flex_column.av-break-at-tablet,
	.responsive #top #wrap_all .av-break-at-tablet .flex_cell {
		margin: 0;
		margin-bottom: 20px;
		width: 100%;
		display: block;
	}

	/*	added 5.0.2: remove 20px only between 767px and 989px   */
	.responsive #top #wrap_all .av-break-at-tablet .flex_cell.no_margin{
		margin-bottom: 0;
	}

	.responsive #top #wrap_all .av-break-at-tablet-flextable,
	.responsive .av-layout-grid-container.av-break-at-tablet {
		display: block;
	}

	.responsive #top #wrap_all .av-break-at-tablet-flextable.av-mobile-columns-flex{
		display: flex;
		flex-direction: column;
	}

	.responsive #top #wrap_all .av-break-at-tablet-flexwrapper.av-column-wrapper-individual.av-mobile-columns-flex{
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	.responsive #top #wrap_all .av-break-at-tablet-flextable.av-mobile-columns-flex.av-columns-reverse,
	.responsive #top #wrap_all .av-break-at-tablet-flexwrapper.av-column-wrapper-individual.av-mobile-columns-flex.av-columns-reverse{
		flex-direction: column-reverse;
	}

	.responsive #top #wrap_all .av-flex-cells .no_margin{
		height:auto !important;
		overflow: hidden;
	}

	.responsive #top #wrap_all .av-layout-grid-container .av-break-at-tablet .av_one_full,
	.responsive #top #wrap_all .av-layout-grid-container .av-break-at-tablet-flextable .av_one_full{
		margin-bottom: 0;		/*  https://github.com/KriesiMedia/wp-themes/issues/4095  */
	}
}


/*  #Mobile (Portrait)
================================================== */

@media only screen and (max-width: 767px)
{
	.responsive .boxed #wrap_all {
		overflow: hidden; /*needed for mobile menu scrolling */
	}

	.responsive #top {
		overflow-x: hidden;
	}

	.responsive .boxed#top, .responsive #top.boxed .stretch_full,
	.responsive.html_boxed.html_header_sticky #header,
	.responsive.html_boxed.html_header_transparency div #header{
		width: 100%;
		max-width: 100%;
	}

	.responsive #top .flex_column_table_cell{
		display: block;
	}

	.responsive #top .flex_column_table{
		display:block;
	}

	.responsive #top #wrap_all .av-mobile-columns-flex{
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	.responsive #top #wrap_all .av-mobile-columns-flex.av-columns-reverse{
		flex-direction: column-reverse;
	}

	.responsive #top #wrap_all .container {
		width: 85%;
		max-width: 85%;
		margin: 0 auto;
		padding-left:0;
		padding-right:0;
		float:none;
	}
	.responsive .units, .responsive .unit {
		margin: 0;
	}

	.responsive #top .container .av-content-small,
	.responsive #top #wrap_all .flex_column,
	.responsive #top #wrap_all .av-flex-cells .no_margin {
		margin: 0;
		margin-bottom: 20px;
		width: 100%;
	}

	.responsive #top #wrap_all .av-flex-cells .no_margin{
		display: block;
		margin: 0;
		height:auto !important;
		overflow: hidden;
		padding-left:8% !important;
		padding-right:8% !important;
	}

	.responsive #top #wrap_all .av-flex-cells .no_margin .flex_cell_inner{
		width: 100%;
		max-width: 100%;
		margin: 0 auto;
	}

	.responsive #top #wrap_all .av-flex-cells .no_margin.av-zero-padding{
		padding-left:0% !important;
		padding-right:0% !important;
	}

	.responsive #top #wrap_all .flex_column:empty{
		margin:0;
	}

	.responsive #top #wrap_all .av-layout-grid-container .av_one_full{
		margin-bottom: 0;		/*  https://github.com/KriesiMedia/wp-themes/issues/4095  */
	}
}
/* #Mobile (Landscape)
================================================== */
/* Note: Design for a width of 480px */
@media only screen and (min-width: 480px) and (max-width: 767px)
{

}

/* #Clearing
================================================== */
/* Self Clearing Goodness */
.container:after {
	content: "\0020";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
/* Use clearfix class on parent to clear nested units,
    or wrap each row of units in a <div class="row"> */
.clearfix:before,
.clearfix:after,
.flex_column:before,
.flex_column:after,
.widget:before,
.widget:after
{
	content: '\0020';
	display: block;
	overflow: hidden;
	visibility: hidden;
	width: 0;
	height: 0;
}

.flex_column:after,
.clearfix:after {
	clear: both;
}
.row,
.clearfix {
	zoom: 1;
}

/* You can also use a <br class="clear" /> to clear units */
.clear {
	clear: both;
	display: block;
	overflow: hidden;
	visibility: hidden;
	width: 0;
	height: 0;
}

/* Columns for better content separation
================================================== */
body div .first,
body div .no_margin {
	margin-left: 0;
}

div .flex_column {
	z-index: 1;
	float: left;
	position: relative;
	min-height: 1px;
	width: 100%;
}
div .av_one_fifth {
	margin-left: 6%;
	width: 15.2%;
}

div .av_one_fourth {
	margin-left: 6%;
	width: 20.5%;
}

div .av_one_third {
	margin-left: 6%;
	width: 29.333333333333332%;
}
div .av_two_fifth {
	margin-left: 6%;
	width: 36.4%;
}
div .av_one_half {
	margin-left: 6%;
	width: 47%;
}
div .av_three_fifth {
	margin-left: 6%;
	width: 57.599999999999994%;
}
div .av_two_third {
	margin-left: 6%;
	width: 64.66666666666666%;
}
div .av_three_fourth {
	margin-left: 6%;
	width: 73.5%;
}
div .av_four_fifth {
	margin-left: 6%;
	width: 78.8%;
}
div .av_one_sixth {
	margin-left: 6%;
	width: 11.666666666666666%;
}
div .av_one_seventh {
	margin-left: 6%;
	width: 9.142857142857142%;
}
div .av_one_eighth {
	margin-left: 6%;
	width: 7.25%;
}
div .av_one_nineth {
	margin-left: 6%;
	width: 5.777777777777778%;
}
div .av_one_tenth {
	margin-left: 6%;
	width: 4.6%;
}
/* Columns for better content separation (no margin)
================================================== */
#top div .no_margin {
	margin-left: 0;
	margin-top: 0;
}
#top .no_margin.av_one_fifth {
	width: 20%;
}
#top .no_margin.av_one_fourth {
	width: 25%;
}
#top .no_margin.av_one_third {
	width: 33.3%;
}
#top .no_margin.av_two_fifth {
	width: 40%;
}
#top .no_margin.av_one_half {
	width: 50%;
}
#top .no_margin.av_three_fifth {
	width: 60%;
}
#top .no_margin.av_two_third {
	width: 66.6%;
}
#top .no_margin.av_three_fourth {
	width: 75%;
}
#top .no_margin.av_four_fifth {
	width: 80%;
}
#top .no_margin.av_one_sixth {
	width: 16.666%;
}
#top .no_margin.av_one_seventh {
	width: 14.285714285714286%;
}
#top .no_margin.av_one_eighth {
	width: 12.5%;
}
#top .no_margin.av_one_nineth {
	width: 11.11111111111111%;
}
#top .no_margin.av_one_tenth {
	width: 10%;
}

/* Columns with equal height
================================================== */

#top .flex_column_table{
	display: table;
	table-layout: fixed;
	width: 100%;
	float: left;	/* added 4.7.4.1 to fix equal height after individual height columns  */
}

#top .flex_column_table.av-equal-height-column-flextable:not(:first-child){
	margin-top: 50px;  /* added ******* to fix inconsistent layout equal height after individual height columns but remove when first entry  */
}

#top .flex_column_table_cell{
	float: none;
	display: table-cell;
}

#top .av-flex-placeholder{
	display: table-cell;
	width: 6%;
}

.av-align-top{
	vertical-align: top;
}

.av-align-middle{
	vertical-align: middle;
}

.av-align-bottom{
	vertical-align: bottom;
}

/*breaking point logic for the common  1/4 element in ipads. makes sure that it converts to a 1/2 element in most cases*/
@media only screen and (min-width: 768px) and (max-width: 989px)
{
	.responsive .av_one_fourth.first.el_before_av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_half.first + .av_one_fourth.el_before_av_one_fourth.flex_column_div,
	.responsive .av_one_half.first + .av_one_fourth.el_before_av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .avia-content-slider-inner .av_one_fourth.flex_column_div{
		margin-left: 4%;
		width: 48%;
	}

	/* added *******  broken layout with 25% width  https://kriesi.at/support/topic/4-column-layout-issues-on-ipad/  */
	.responsive #top .no_margin.av_one_fourth{
		margin-left: 0;
		width: 50%;
	}

	.responsive .av_one_fourth.first.el_before_av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_half.first + .av_one_fourth.el_before_av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_half.flex_column_div,
	.responsive .avia-content-slider-inner .av_one_fourth.first.flex_column_div{
		margin-left: 0%;
		clear: both;
	}

	.responsive .av_one_half.first.el_before_av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_half.flex_column_div{
		width: 100%;
	}

	.responsive .av_one_half.first + .av_one_fourth.flex_column_div,
	.responsive .av_one_half.first + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_fourth + .av_one_fourth.flex_column_div,
	.responsive .av_one_fourth.first + .av_one_fourth + .av_one_half.flex_column_div{
		margin-top: 30px;
	}
}
