
/* ======================================================================================================================================================
HEADING
====================================================================================================================================================== */
.av-special-heading{
	width:100%;
	clear:both;
	display: block;
	margin-top:50px;
	position: relative;
}

.avia_mobile .av-special-heading{
	-webkit-perspective: 1000px;
	-webkit-backface-visibility: hidden;
}

.av-special-heading.avia-builder-el-no-sibling{
	margin-top:0px;
	margin-bottom:0px;
}

.flex_column + .av-special-heading{
	float:left;
}

body .av-special-heading .av-special-heading-tag{
	padding:0;
	margin:0;
	float:left;
}

.meta-heading .av-special-heading-tag{
	font-weight: normal;
}

.custom-color-heading .av-special-heading-tag {
	color: inherit;
}

.special-heading-border{
	position: relative;
	overflow: hidden;
}

.av-special-heading-h1 .special-heading-border{
	height: 3.4em;
}

.av-special-heading-h2 .special-heading-border{
	height: 2.6em;
}

.av-special-heading-h3 .special-heading-border{
	height: 1.9em;
}

.av-special-heading-h4 .special-heading-border{
	height: 1.6em;
}

.av-special-heading-h5 .special-heading-border{
	height: 1.5em;
}

.av-special-heading-h6 .special-heading-border{
	height: 1.4em;
}

.special-heading-inner-border{
	display: block;
	width:100%;
	margin-left:15px;
	border-top-style: solid;
	border-top-width:1px;
	position: relative;
	top:50%;
	opacity: 0.15;
}

.meta-heading .special-heading-inner-border{
	opacity: 0.2;
}

.custom-color-heading .special-heading-inner-border{
	opacity: 0.4;
}

#top #wrap_all .custom-color-heading .av-special-heading-tag,
#top .custom-color-heading a,
#top .custom-color-heading strong,
#top .custom-color-heading .special_amp{
	color: inherit;
}

#top .custom-color-heading a{
	text-decoration: underline;
}
#top .av-special-heading.av-icon.custom-color-heading a.av-heading-link{
	text-decoration: none;
}
#top .custom-color-heading a:hover{
	opacity:0.8;
	text-decoration: none;
}

#top #wrap_all .av-inherit-size .av-special-heading-tag{
	font-size: 1em;
}

.av-thin-font .av-special-heading-tag,
.modern-quote .av-special-heading-tag{
	font-weight: 300;
}

.av-thin-font strong,
.modern-quote strong{
	font-weight: 600;
}

body .av-special-heading.modern-centered{
	text-align: center;
}

body .av-special-heading.modern-right{
	text-align: right;
}

body .av-special-heading.elegant-centered{
	text-align: center;
}

body .av-special-heading.elegant-centered .av-special-heading-tag{
	position: relative;
	overflow: hidden;
}

body .av-special-heading.elegant-centered .av-special-heading-tag .heading-wrap:before,
body .av-special-heading.elegant-centered .av-special-heading-tag .heading-wrap:after {
	content: "";
	position: absolute;
	height: 1px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-color: inherit;
	top: 50%;
	transform: translateY(-50%);
	width: 1000px;
}

body .av-special-heading.elegant-centered .av-special-heading-tag .heading-wrap:before {
	right: 100%;
	margin-right: 1em;
}

body .av-special-heading.elegant-centered .av-special-heading-tag .heading-wrap:after {
	left: 100%;
	margin-left: 1em;
}

body .av-special-heading.elegant-centered .av-special-heading-tag .heading-wrap{
	display: inline-block;
	position: relative;
}

body .av-special-heading.elegant-centered .av-special-heading-tag .heading-char{
	display: block;
	font-size: 1em;
	line-height: 1;
}



/*quote style*/
body .av-special-heading.blockquote > *{
	white-space: normal;
	float: none;
}

.av-special-heading.classic-quote{
	text-align: center;
}

.av-special-heading.classic-quote.classic-quote-left{
	text-align: left;
}

.av-special-heading.classic-quote.classic-quote-right{
	text-align: right;
}



body .av-special-heading.classic-quote > *{
	display:block;
	font-family: "Droid Serif", Georgia, Times, serif;
	font-weight: normal;
	font-style: italic;
	float: none;
}

body .av-special-heading.blockquote .special-heading-border{
	display:none;
}

/*linked header*/
#top .av-special-heading.av-linked-heading a:hover{
	opacity: 0.5;
}



/*subheading*/

.av-subheading{
	font-size: 15px;
	line-height: 1.3em;
}

.av-subheading p:first-child{
	margin-top:0;
}

.av-subheading p:last-child{
	margin-bottom:0;
}

.av-subheading_below{
	margin-top:0.3em;
}

.av-subheading_above{
	margin-bottom:0.3em;
}

/*.av-subheading.av_custom_color{opacity: 0.8;}  removed 4.9.2.2  */


/* Accessibility rules */
#top.av-accessibility-aaa .av-subheading {
	line-height: 1.5em;
}
