/* ======================================================================================================================================================
Progress Bar
====================================================================================================================================================== */
.avia-progress-bar-container{
	margin: 15px 0;
	clear:both;
}

.avia-progress-bar{
	position: relative;
	margin-bottom:4px;
}

.avia-progress-bar .progressbar-title-wrap{
	position: absolute;
	z-index: 2;
	bottom: 3px;
	left: 3px;
	color: white;
	background: black;
	background: rgba(0, 0, 0, 0.5);
	padding: 1px 10px 2px 10px;
	border-radius: 3px;
	line-height: 21px;
}

.progressbar-icon{
	float:left;
	margin-right:5px;
}

.icon-bar-no .progressbar-icon{
	display:none;
}

.progressbar-title{
	float:left;
}

.avia-progress-bar .progress{
	width: 100%;
	height: 30px;
	line-height: 30px;
	position: relative;
	border-radius: 3px;
	overflow: hidden;
	box-shadow: inset 0px 0px 15px 0px rgba(0, 0, 0, 0.07);
}

#top .avia-progress-bar div.progress .bar-outer,
#top .avia-progress-bar div.progress .bar{
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	background-size: 32px 32px;
	border-radius: 3px;
}

.av-animated-bar .avia-progress-bar div.progress .bar{
	animation: avia-bg-move 1.2s linear infinite;
	height: 100%;
}

.avia-progress-bar div.progress{
	clear: both;
}

.html-admin-preview #top div.progress .bar-outer,
.avia_transform #top div.progress .bar-outer{
	width:0;
}


.av-small-bar .avia-progress-bar{
	margin-top:1.9em;
}

.av-small-bar .avia-progress-bar:first-child{
	margin-top:0em;
}

.av-small-bar .avia-progress-bar .progressbar-title-wrap{
	position: relative;
    bottom: 0;
    left: 0;
    padding: 0;
    background: none;
    color: inherit;
    line-height: 1.65em;
    font-size: 0.9em;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.av-small-bar .avia-progress-bar .progress{
    height: 10px;
    line-height: 1em;
    border-radius: 0px;
}

.progressbar-percent{
	float:right;
	opacity: 0.6;
}

#top .av-small-bar .avia-progress-bar div.progress .bar-outer,
#top .av-small-bar .avia-progress-bar div.progress .bar{
	border-radius: 0px;
}

.html-admin-preview #top .avia-progress-bar div.progress.avia_start_animation .bar-outer,
.avia_transform #top .avia-progress-bar div.progress.avia_start_animation .bar-outer{
	animation: avia_expand 1.5s cubic-bezier(0.165, 0.840, 0.440, 1.000);  /* IE 10+ */
	width: 100%;
}
