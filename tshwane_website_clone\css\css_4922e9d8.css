/* ======================================================================================================================================================
#Sidebar & Widgets
====================================================================================================================================================== */

.sidebar .widget:first-child,
.content .sidebar .widget:first-child{
	padding-top:0;
	border-top-style: none;
	border-top-width: 0;
}

/* archive widget */
.widget_archive label.screen-reader-text {
    display: none;
}


/*nav menu widget*/
.widget_nav_menu a{
	display: block;
	padding:4px 0px 5px 0;
	text-decoration: none;
}

div .widget_nav_menu{
	padding-bottom:24px;
}

#top .widget_nav_menu ul{
	margin:0;
	padding:0;
	float: none;
	list-style-type: none;
}

#top .widget_nav_menu li{
	position: relative;
	box-sizing: content-box;
	clear:both;
	font-size:13px;
}

#top #footer .widget_nav_menu li{
	background-color:transparent;
}

#top .widget_nav_menu ul ul li:before {
	content: "\2219";
	position: absolute;
	top:5px;
}

#top .sidebar_left .widget_nav_menu ul ul li:before {
    right: -10px;
}

#top .sidebar_left.sidebar_align_left .widget_nav_menu ul ul li:before {
    right: auto;
    left: 0;
}

#top .widget_nav_menu ul ul li a{
	padding:6px 0px 7px 12px;
}

.widget_nav_menu .current-menu-item>a,
.widget_nav_menu .current_page_item>a{
	font-weight: bold;
}

.sidebar .widget_nav_menu ul:first-child>.current-menu-item,
.sidebar .widget_nav_menu ul:first-child>.current_page_item,
.sidebar .widget_nav_menu ul:first-child>.current-menu-ancestor{
	padding-left: 51px;
	left: -51px;
	top:1px;
	margin-top: -1px;
	padding-top:1px;
	width:100%;
	box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2);
	margin-bottom: 4px;
}

.widget_nav_menu ul ul{
	display:block;
}

.widget_nav_menu.widget_nav_hide_child ul ul{
	display:none;
}

#top .widget_nav_menu .current-menu-item > ul,
#top .widget_nav_menu .current_page_item > ul,
#top .widget_nav_menu .current_page_ancestor > ul,
#top .widget_nav_menu .current-menu-ancestor > ul{
	display:block;
/*	padding:0 0 0 13px;*/
}

#top .widget_nav_menu .sub-menu > li{
	display: block;
	padding: 0 0 0 13px;
}

#top .sidebar_left .widget_nav_menu .current-menu-item > ul,
#top .sidebar_left .widget_nav_menu .current_page_item > ul,
#top .sidebar_left .widget_nav_menu .current_page_ancestor > ul,
#top .sidebar_left .widget_nav_menu .current-menu-ancestor > ul{
	padding:0 13px 0 0 ;
}

#top .sidebar_left.sidebar_align_left .widget_nav_menu .current-menu-item > ul,
#top .sidebar_left.sidebar_align_left .widget_nav_menu .current_page_item > ul,
#top .sidebar_left.sidebar_align_left .widget_nav_menu .current_page_ancestor > ul,
#top .sidebar_left.sidebar_align_left .widget_nav_menu .current-menu-ancestor > ul{
    padding:0 0 0 13px ;
}


#top .sidebar_left .widget_nav_menu .sub-menu{
	padding-right: 13px;
}

#top .sidebar_left.sidebar_align_left .widget_nav_menu .sub-menu{
    padding-left: 13px;
    padding-right: 0;
}


.widget_nav_menu ul:first-child>.current-menu-item>a,
.widget_nav_menu ul:first-child>.current_page_item>a{
	border:none;
	padding:6px 7px 7px 0;
}

#top .widget_nav_menu ul ul .sub-menu,
#top .widget_nav_menu ul ul .children{
	padding:0 0 0 10px;
	overflow: hidden;
	margin:0;
}

.widget_nav_menu .nested_nav>li:first-child>a{
	border:none;
}

#top .widget_nav_menu .nested_nav{
	padding-bottom:30px;
}

#top .content .flex_column .widget_nav_menu li a {
	padding: 0.8em 3px;
}

#top .content .flex_column .widget_nav_menu li a:hover,
#top .content .flex_column .widget_nav_menu ul:first-child>.current-menu-item,
#top .content .flex_column .widget_nav_menu ul:first-child>.current_page_item{
	background-color: rgba(255,255,255,0.4);
}

#top .content .flex_column .widget_nav_menu li {
	background-color: transparent;
	margin: 0;
	padding:0;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}

#top .content .flex_column .widget_nav_menu li:first-child {
	border-top-style: solid;
	border-top-width: 1px;
}

/*instagram*/

.av-instagram-pics{
	display:table;
	width:100%;
	table-layout: fixed;
}
.av-instagram-row{
	display:table-row;
}

.av-instagram-item{
	display: table-cell;
	padding: 3px;
	height:100%;
}

.av-instagram-item a{
	display:block;
	position: relative;
	padding-bottom:100%;
	width:100%;
	background-size: cover;
}

.av-instagram-item img{
	display:block;
}

.av-instagram-item .image-overlay.overlay-type-image {
	left: 0;
	width: 100%;
}

.av-instagram-item:first-child {
	padding-left: 0px;
}

.av-instagram-item:last-child  {
	padding-right: 0px;
}

.av-instagram-row:first-child .av-instagram-item{
	padding-top: 0px;
}

.av-instagram-row:last-child .av-instagram-item {
	padding-bottom: 0px;
}

.av-instagram-follow{
	width:100%;
	text-align: center;
	display: block;
	margin-top:3px;
}

.avia-instagram-feed .av-instagram-errors-msg.av-instagram-admin{
	color: #cc0033;
}

/*mailchimp*/
#top .av-mailchimp-widget fieldset,
#top .av-mailchimp-widget form{
	margin:0;
}

#top .av-mailchimp-widget p{
	margin:2px 0;
}

#top .av-mailchimp-widget-style-boxed_form{
	padding:15px;
	border-style: solid;
	border-width: 1px;
}

#top .av-mailchimp-widget .button{
	margin-top:5px;
	width:100%;
	padding: 13px 10px;
}

.widgettitle + .av-mailchimp-widget-style-boxed_form{
	margin-top:-10px;
}

#top .av-form-error-container{
	border: 1px solid #BB1313;
	padding: 15px;
	color: #BB1313;
	line-height: 1.4em;
	font-size: 13px;
	margin-bottom: 20px;
}

/*text*/
.textwidget ul{
	margin-left:0px;
	overflow: hidden;
}

/*combo widget*/
.js_active .avia_combo_widget .tab_titles{
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

.js_active #top .avia_combo_widget .active_tab{
	border-bottom-style: solid;
	border-bottom-width: 2px;
	border-bottom-color: initial;
}

.js_active #top .avia_combo_widget .tab_content {
	padding:0;
	background: transparent;
	border: none;
}

.js_active .avia_combo_widget .top_tab .tab{
	border: none;
	background: transparent;
	padding:5px 10px;
	border-bottom-style: solid;
	border-bottom-width: 2px;
	border-bottom-color: transparent;
}

/*likebox*/
.av_facebook_widget_wrap{
	overflow: hidden;
	position: relative;
	padding: 0px;
	background: #fff;
}

.av_facebook_widget_wrap_border_yes{
	border-style: solid;
	border-width: 1px;
}

.av_facebook_widget_wrap_positioner iframe{
	position: absolute;
	height:100%;
	width:100%;
}

.av_facebook_widget_wrap_positioner{}

.av_facebook_widget{
	width:100%;
}

.av_facebook_widget {
	width: 107%;
	max-width: 137%;
	left: -5px;
	top: -4px;
	position: relative;
}

.avia_fb_likebox .av_facebook_widget_page_title{
	font-size: 1.2em;
	font-weight: 700;
	margin: 12px 0;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_link{
	display: inline-block;
	position: relative;
	width: 100%;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_link img{
	width: 100%;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_link:hover{
	text-decoration: none;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_text{
	font-size: 1.5em;
	position: absolute;
	padding: 25px;
	text-align: center;
	top: 0;
	height: 100%;
	display: flex;
	align-items: center;
	opacity: 0;
	width: 100%;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_text span{
	width: 100%;
}

.widget .av_widget_img_text_confirm .av_img_text_confirm_text:hover{
	opacity: 1;
}


.avia_fb_likebox .av_facebook_widget_main_wrap{
	background-attachment: scroll;
	background-size: cover;
	background-position: center center;
	width:100%;
	min-width:180px;
	min-height:214px;
	position: relative;
	font-family: Helvetica, Arial, sans-serif;
}

.avia_fb_likebox .av_facebook_widget_main_wrap_shadow{
	position: absolute;
	top:0;
	left:0;right:0;
	height:90px;
	background: linear-gradient(to bottom, rgba(0, 0, 0, .7) 0%, rgba(0, 0, 0, 0) 100%);
	z-index: 0;
}

.avia_fb_likebox .av_facebook_widget_logo_image{
	position: absolute;
	height:54px;
	width:54px;
	top:8px;
	left:8px;
	background:#fff;
	box-shadow: 0 1px 6px rgba(0, 0, 0, .5);
	border: 2px solid #fff;
	z-index: 2;
}

.avia_fb_likebox .av_facebook_widget_logo_image img{
	max-width: 50px;
	max-height:50px;
	text-align: center;
	display: inline-block;
}

.avia_fb_likebox  .av_facebook_widget_page_title_container{
	color:#fff;
	margin: 0px 18px 0 68px;
	position: relative;
	z-index: 2;
	font-size: 18px;
    font-weight: 500;
    line-height: 1.358;
    margin-bottom: -5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 2px 4px rgba(0, 0, 0, .9);
    top:8px;
	min-height: 95px;
}

#top .avia_fb_likebox  .av_facebook_widget_page_title_container a{
	color:#fff;
}

.avia_fb_likebox  .av_facebook_widget_page_title_container .av_facebook_widget_content{
	font-size: 12px;
}



.avia_fb_likebox  .av_facebook_widget_page_title_container span{
	display: block;
}


.avia_fb_likebox .av_facebook_widget_add_info{
	width:100%;
	min-height: 83px;
	background: #f6f7f9;
    border: 1px solid #e9ebee;
    border-top: 0;
	font-size: 12px;
    line-height: 16px;
    color: #4b4f56;
    padding:8px;
}

.avia_fb_likebox .av_facebook_widget_add_info_inner{
	background: #fff;
    border-color: #e9ebee #dfe0e4 #d0d1d5;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .12);
    position: relative;
    min-height: 66px;
    padding:8px;
}

.avia_fb_likebox .av_facebook_widget_imagebar{
	height:32px;
	background-repeat: repeat;
	background-size: auto 100%;
	background-image:url("../images/layout/fake_facebook.jpg");
}

.avia_fb_likebox .av_facebook_widget_add_info_inner_wrap{
	min-height: 15px;
	padding-bottom: 4px;
	display: block;
}

.avia_fb_likebox .av_facebook_widget_button{
	background-color: #f6f7f9;
    border-color: #ced0d4;
    color: #4b4f56;
    line-height: 22px;
    border: 1px solid rgba(0, 0, 0, .12);
    font-size: 12px;
    padding: 0 5px;
    display: inline-block;
    margin:8px;
}

.avia_fb_likebox .av_facebook_widget_icon {
    display: inline-block;
    margin-right: 5px;
    background: #3a5797;
    height: 16px;
    width: 16px;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    font-size: 11px;
    line-height: 18px;
}

.avia_fb_likebox .ajax_load{
	display: none;
}

.avia_fb_likebox.ajax_loading_now .ajax_load{
	display: block;
}


/*social media count widget*/
.avia_socialcount{
	overflow: hidden;
	text-align: left;
}

#top .social_widget_icon{
	padding:0;
	margin:0;
	height:30px;
	width:30px;
	line-height: 32px;
	text-align: center;
	float: left;
	font-size: 17px;
	border-radius: 2px;
}

.asc_twitter .social_widget_icon{
	color:#000;
	background-color:#fff;
}

.asc_rss .social_widget_icon{
	color:#fff;
	background-color:#ffa133;
	border-color:#ffa133;
	text-shadow: 1px 1px 1px #D18021;
}

.avia_socialcount .seperator{
	display:none;
}

div #footer .avia_socialcount{
	padding:10px 0 0 0;
}

.asc_multi_count{
	float:left;
	width:50%;
}

.avia_socialcount a,
.avia_socialcount a:hover{
	height:40px;
	text-decoration: none;
	display:block;
	min-width:89px;
}

.avia_socialcount strong,
.avia_socialcount span{
	display:block;
	line-height: 1em;
	padding:0 0 0 36px;
}

.avia_socialcount a strong{
	font-size: 11px;
	font-weight: bold;
	letter-spacing: 0;
	padding-top: 4px;
}

.avia_socialcount a span{
	font-size:10px;
	padding-top:3px;
}


/*twitter widget*/
#top .widget.tweetbox .tweets{
	list-style-type: none;
	list-style-position: outside;
	border:none;
}

#top .widget.tweetbox .tweet{
	padding:10px 0;
	line-height: 18px;
	position: relative;
	overflow: hidden;
	font-size:11px;
	background: none;
	border-top-style: dashed;
	border-top-width: 1px;
	margin:0;
}

#top .widget.tweetbox .tweet:first-child{
	border:none;
}

.widget.tweetbox .tweet-thumb{
	position:relative;
}

.widget.tweetbox .tweet-thumb a{
	margin-right:9px;
	padding:3px;
	float:left;
	border-style: solid;
	border-width: 1px;
	margin-top: 5px;
	display:block;
	width:36px;
}

#top .widget.tweetbox .tweet-thumb img{
	display:block;
	float:left;
	border:none;
	padding:0;
	margin:0;
}

.widget.tweetbox .tweet-text{
	position: relative;
	overflow: hidden;
}

.widget.tweetbox .tweet-time{
	clear: both;
	font-size:11px;
}

.widget.tweetbox .tweet-text.avatar_no {
	font-size: 12px;
	line-height: 1.7em;
	font-weight: 400;
}

.widget.tweetbox .tweet-text.avatar_no .tweet-time{
	font-style: italic;
	font-weight: normal;
}


/*advertising widget*/
.avia_partner_widget{
	overflow: hidden;
	clear:both;
}

.avia_partner_widget a,
.avia_partner_widget a:hover {
	float: left;
	display: block;
	text-decoration: none;
	width: 49.5%;
	outline: none;
	border: none;
	padding-bottom: 49.5%;
	position: relative;
	margin-right: 1%;
}

.avia_partner_widget a.avia_partner2,
.avia_partner_widget a.avia_partner2:hover {
	margin-right: 0;
}

#top .avia_partner_widget{
	max-width:304px;
}

#top .avia_partner_widget img{
	position: absolute;
	left:0;
	top:0;
}

.avia_partner1{
	margin-right:1px;
}

.avia_parnter_empty{
	line-height: 1em;
	height:97%;
	width:97%;
	display: block;
	font-size:10px;
	text-align: center;
	position: absolute;
	border-style: solid;
	border-width: 1px;
}

.avia_parnter_empty span{
	width:100%;
	position: absolute;
	top:50%;
	margin-top:-5px;
	text-align: center;
	left:0;
}

/*Google Maps*/
.widget .avia-google-map-container{
	height:230px;
	width:100%;
}

.widget .avia-google-map-container{
	background: no-repeat center;
	background-size: cover;
}

.widget .content .avia-google-map-container{
	height:230px;
}

#top .widget .infoWindow  input[type="text"]{
	margin: 0 0px 3px 0;
}

.widget .avia-google-map-container img,
.widget .avia-google-map-container a img{
	max-width: none;
}

.widget .avia-google-map-container div,
.widget .avia-google-map-container img,
.widget .avia-google-map-container a{
	box-sizing: content-box;
}

.widget .avia-google-map-container .av_text_confirm_link{
    padding: 25px;
    text-align: center;
    opacity: 0;
    position: absolute;
    width: 150px;
    left: 50%;
    top: 50%;
    margin-left: -100px;
    margin-top: -50px;
    background: rgba(0,0,0,0.7);
    border-radius: 3px;
    color: #fff;
    font-size: 1em;
    line-height: 1.3em;
	transition: all 0.3s ease-in-out;
}

.widget .avia-google-map-container .av_text_confirm_link:hover{
	color: #fff;
    background: rgba(0,0,0,0.9);
}

.widget .avia-google-map-container.av_gmaps_show_delayed.av-no-fallback-img .av_text_confirm_link,
.widget .avia-google-map-container.av_gmaps_show_page_only.av-no-fallback-img .av_text_confirm_link{
	height: auto;
	opacity: 1;
}

.widget .avia-google-map-container .av_text_confirm_link span{
	width: 100%;
}

.widget .avia-google-map-container:hover .av_text_confirm_link{
	opacity: 1;
	text-decoration: none;
}


/*news widget*/
#top .news-content{
	padding:7px 0 3px 0;
	line-height: 1.7em;
	position: relative;
	overflow: hidden;
	font-size:0.85em;
	display: block;
	clear: both;
	margin:0;
}

#top .news-wrap{
	border:none;
	list-style-type: none;
	list-style-position: outside;
}

#top .news-wrap li{
	border:none;
	background: none;
	text-indent: 0;
}

.news-link,
.news-link:hover,
#top .widget ul li .news-link:hover{
	display: block;
	position: relative;
	text-decoration: none;
	overflow: hidden;
	z-index: 2;
}


/*default size news/portfolio widget*/
.news-thumb{
	margin-right:9px;
	padding:3px;
	float:left;
	margin-top: 4px;
	border-style: solid;
	border-width: 1px;
	height:36px;
	width:36px;
	display: block;
}

.news-thumb img{
	width:28px;
	height:28px;
}


/*bigger size news/portfolio widget*/
.image_size_portfolio_small .news-link{
	float:left;
	margin-right:10px;
}

.image_size_portfolio_small .news-thumb,
.image_size_portfolio_small .tweet-thumb img,
.image_size_portfolio_small .news-thumb img{
	width:150px;
	min-height:100px;
}

.image_size_portfolio_small .news-excerpt{
	overflow: hidden;
	font-size: 13px;
	line-height: 1.65em;
}

.image_size_portfolio_small .news-headline{
	font-size:14px;
}

#top .news-thumb img{
	display:block;
	float:left;
	border:none;
	padding:0;
	margin:0;
}

.news-headline{
	overflow: hidden;
	font-weight: bold;
	margin-top:2px;
	display:block;
}

.news-time{
	font-weight:normal;
	clear: both;
	font-size:0.92em;
	display:block;
}

.news-excerpt{}


/*tagcloud*/
.tagcloud br{
	display:none;
}

.tagcloud a{
	font-size:11px !important;
	padding:2px 8px;
	margin:0 1px 1px 0;
	display:block;
	float:left;
	border-style: solid;
	border-width: 1px;
	text-decoration: none;
}

.tagcloud a:hover{
	text-shadow: none;
	text-decoration: underline;
}

.widget_tag_cloud h3{
	border:none;
}


/*rss*/
.widget_rss li{
	line-height: 1.5em;
	font-size: 11px;
}

.widget_rss li div{
	font-family: "Georgia", "Times New Roman", Helvetica, Arial, sans-serif;
	font-style: italic;
}

.rsswidget{
	display:block;
	font-weight: bold;
}

.rss-date,
.widget_rss cite{
	font-size:11px;
}

.widget_rss li{
	padding:7px 0;
}

.widget_rss .widgettitle img{
	display:none;
}


/*recentcomments, recent entries*/
.recentcomments,
.widget_recent_entries li{
	padding:7px 0;
	display:block;
	font-size: 0.85em;
	line-height: 1.5em;
}

.recentcomments a,
.widget_recent_entries li a{
	font-style: italic;
	font-family: "Georgia", "Times New Roman", Helvetica, Arial, sans-serif;
}

.recentcomments,
.widget_recent_entries li{
	border-top-width:3px;
	border-top-style: solid;
}

.recentcomments:first-child,
.widget_recent_entries li:first-child{
	border-top:none;
}


/*sidebar left mods for all widgets*/
.sidebar_left .widget_nav_menu ul:first-child>.current-menu-item,
.sidebar_left .widget_nav_menu ul:first-child>.current_page_item,
.sidebar_left .widget_nav_menu ul:first-child>.current-menu-ancestor{
	padding-right: 52px;
	padding-left:0;
	left: auto;
	top:1px;
}

.sidebar_left.sidebar{
	text-align: right;
}

.sidebar_left .news-thumb{
	float:right;
	margin: 4px 0 0 9px;
}


/*TWITTER WIDGET PRO STYLES*/
#top .widget_twitter{
	font-size: 12px;
	line-height: 1.65em;
}

#top .widget_twitter .twitter-avatar{
	float:left;
	margin-right:13px;
	border-style: solid;
	border-width:1px;
	padding:3px;
	display: block;
}

#top .widget_twitter .twitter-avatar a,
#top .widget_twitter .twitter-avatar img{
	display: block;
}

#top .widget_twitter ul{
	overflow: hidden;
	margin:0;
	padding:0;
}

#top .widget_twitter li{
	padding: 0 0 8px 0;
	margin: 0 0 8px 0;
	border-bottom-style: solid;
	border-bottom-width: 2px;
}

#top .widget_twitter li:last-child{
	border-bottom: none;
}

#top .widget_twitter .entry-content-wrapper{
	padding:0;
	margin:0;
	float: none;
	clear: both;
	position: relative;
	border:none;
	width:100%;
}

#top .widget_twitter .entry-meta{
	display: block;
	font-size: 11px;
	font-style: italic;
	opacity: 0.8;
	margin-top:5px;
}

#top .widget_twitter .time-meta,
#top .widget_twitter .from-meta{}

#top .widget_twitter .intent-meta{
	display: block;
	font-size: 11px;
	margin-top:8px;
}

#top .widget_twitter .intent-meta a{
	text-decoration: none;
	margin-right: 4px;
}

#top .widget_twitter .intent-meta a:hover{
	text-decoration: underline;
}


/* user defined text alignmnt for sidebars */
.sidebar_left.sidebar_align_left.sidebar{
	text-align: left;
}

.sidebar_left.sidebar_align_left .news-thumb{
	float:left;
	margin: 4px 9px 0 0
}

.sidebar_left.sidebar_align_left .widget_nav_menu ul:first-child>.current-menu-item,
.sidebar_left.sidebar_align_left .widget_nav_menu ul:first-child>.current_page_item,
.sidebar_left.sidebar_align_left .widget_nav_menu ul:first-child>.current-menu-ancestor{
    padding-left: 52px;
    padding-right:0;
    left: 0;
    right: auto;
    top:1px;
}


/* Table of Contents */
.avia-toc-container{
    position: relative;
}

.avia-toc-container a {
    display: block;
    position: relative;
    line-height: 1.4em;
}

.avia-toc-container a:hover {
    text-decoration: none;
}

/* simple style */
.avia-toc-style-simple a{
    overflow-x: hidden;
    margin-bottom: 1em;
    min-height: 25px;
}

.avia-toc-style-simple a span{
    background-color: #fff;
    position: relative;
    padding-right: 5px;
    z-index: 2;
}


.avia-toc-style-simple a:after {
    float: left;
    width: 0;
    color: rgba(0,0,0,0.25);
    font-size: 9px;
    font-weight: normal;
    white-space: nowrap;
    content:
            ". . . . . . . . . . . . . . . . . . . . "
            ". . . . . . . . . . . . . . . . . . . . "
            ". . . . . . . . . . . . . . . . . . . . "
            ". . . . . . . . . . . . . . . . . . . . "
}

.avia-toc-style-simple .avia-toc-level-0:after {
	line-height: 3;
}

.avia-toc-style-simple .avia-toc-level-1:after {
	line-height: 3;
}

.avia-toc-style-simple .avia-toc-level-2:after {
	line-height: 2.9;
}

.avia-toc-style-simple .avia-toc-level-3:after {
	line-height: 2.7;
}

.avia-toc-style-simple .avia-toc-level-4:after {
	line-height: 2.5;
}

.avia-toc-style-simple .avia-toc-level-5:after {
	line-height: 2.4;
}

.avia-toc-style-simple .avia-toc-level-0 {
	font-weight: bold;
	font-size: 1em;
}

.avia-toc-style-simple .avia-toc-level-2 {
	font-weight: normal;
	font-size: 0.9375em;
}

.avia-toc-style-simple .avia-toc-level-3 {
	font-weight: normal;
	font-size: 0.875em;
}

.avia-toc-style-simple .avia-toc-level-4 {
	font-weight: normal;
	font-size: 0.8125em;
}

.avia-toc-style-simple .avia-toc-level-5 {
	font-weight: normal;
	font-size: 0.8125em;
	font-style: italic;
}

/* indentation */
.avia-toc-style-simple.avia-toc-indent .avia-toc-level-1 {
	margin-left: 0.625em
}

.avia-toc-style-simple.avia-toc-indent .avia-toc-level-2 {
	margin-left: 1em
}

.avia-toc-style-simple.avia-toc-indent .avia-toc-level-3 {
	margin-left: 1.428em
}

.avia-toc-style-simple.avia-toc-indent .avia-toc-level-4 {
	margin-left: 2.307em
}

.avia-toc-style-simple.avia-toc-indent .avia-toc-level-5 {
	margin-left: 2.692em
}

/* elegant style */
.avia-toc-style-elegant{
    padding-left: 30px;
}

.avia-toc-style-elegant a {
    text-transform: uppercase;
    padding-bottom: 1.5em;
}

.avia-toc-style-elegant .avia-toc-level-0{
	font-weight: bold;
	font-size: 0.875em;
}

.avia-toc-style-elegant .avia-toc-level-1{
	font-weight: normal;
	font-size: 0.875em;
}

.avia-toc-style-elegant .avia-toc-level-2{
	font-weight: normal;
	font-size: 0.8125em;
}

.avia-toc-style-elegant .avia-toc-level-3{
	font-weight: bold;
	font-size: 0.8125em;
}

.avia-toc-style-elegant .avia-toc-level-4{
	font-weight: normal;
	font-size: 0.75em
}

.avia-toc-style-elegant .avia-toc-level-5{
	font-weight: normal;
	font-size: 0.75em;
	font-style: italic;
}


.avia-toc-style-elegant a:before{
    content: '';
    position: absolute;
    height: calc(100% + 0.3em);
    left: -20px;
    top: 0.15em;
    border-left-width: 2px;
    border-left-style: solid;
    border-left-color: rgba(0,0,0,0.08);
}

.avia-toc-style-elegant a.avia-toc-level-0:last-child:after,
.avia-toc-style-elegant a:first-child:after,
.avia-toc-style-elegant a.avia-toc-level-0:after,
.avia-toc-style-elegant a:first-child span:after,
.avia-toc-style-elegant a.avia-toc-level-0 span:after{
    content: '';
    position: absolute;
    width: 9px;
    height: 9px;
    border-width: 3px;
    border-style: solid;
    border-radius: 9px;
    left: -26px;
    top: 0.15em;
    z-index: 2;
}

.avia-toc-style-elegant a:first-child span:after,
.avia-toc-style-elegant a.avia-toc-level-0 span:after{
    border-color: transparent;
    z-index: 1;
}

.avia-toc-style-elegant a:first-child:hover span:after,
.avia-toc-style-elegant a.avia-toc-level-0:hover span:after{
    animation: sonarEffect 2s ease-out infinite;
}

.avia-toc-style-elegant a:last-child:before{
    display: none;
}

.avia-toc-style-elegant a:last-child:after{
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    left: -23px;
    top: 0.4em;
    border-radius: 8px;
}

/* indentation */
.avia-toc-style-elegant.avia-toc-indent .avia-toc-level-1{
	padding-left: 0.625em
}
.avia-toc-style-elegant.avia-toc-indent .avia-toc-level-2{
	padding-left: 1em
}
.avia-toc-style-elegant.avia-toc-indent .avia-toc-level-3{
	padding-left: 1.428em
}
.avia-toc-style-elegant.avia-toc-indent .avia-toc-level-4{
	padding-left: 2.307em
}
.avia-toc-style-elegant.avia-toc-indent .avia-toc-level-5{
	padding-left: 2.692em
}

