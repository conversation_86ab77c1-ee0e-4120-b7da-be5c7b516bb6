
/*site preloader: http://projects.lukehaas.me/css-loaders/ */
.av-siteloader-wrap{
	position: fixed;
	top:0;
	left:0;
	right:0;
	bottom:0;
	width:100%;
	height:100%;
	z-index: 1000000;
	background: #fff;
	display:none;
}

html.av-preloader-active{}

html.av-preloader-active .av-siteloader-wrap{
	display:block;
}

.av-siteloader-inner{
	position: relative;
	display: table;
	width: 100%;
	height:100%;
	text-align: center;
}

.av-siteloader-cell{
	display:table-cell;
	vertical-align: middle;
}

.av-siteloader,
#top div.avia-popup .mfp-preloader{
	font-size: 10px;
	position: relative;
	text-indent: -9999em;
	margin:0 auto;
	border-top: 	2px solid rgba(0, 0, 0, 0.2);
	border-right: 2px solid rgba(0, 0, 0, 0.2);
	border-bottom:2px solid rgba(0, 0, 0, 0.2);
	border-left:  2px solid #000;
	animation: av-load8  0.8s infinite linear;
}

#top div.avia-popup .mfp-preloader{
	position: absolute;
	background: transparent;
	border-top: 2px solid #fff;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
}

.av-preloader-reactive #top .av-siteloader{
	animation: avia_pop_loader 1.3s 1 linear;
}

.av-preloader-reactive #top .av-siteloader-extra{
	border-radius: 50%;
	width: 400px;
	height: 400px;
	background:#eee;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: scale(0);
	opacity:0;
	animation: avia_shrink 1.1s 1 linear;
	margin:-200px 0 0 -200px;
}

.av-siteloader,
#top div.avia-popup .mfp-preloader,
.av-siteloader:after ,
#top div.avia-popup .mfp-preloader:after{
	border-radius: 50%;
	width: 40px;
	height: 40px;
}

.av-preloading-logo{
	position: relative;
	margin: 0 auto;
	display: block;
	max-width:450px;
	max-height:450px;
	margin-bottom:20px;
	z-index: 100;
}

#top div.avia-popup .mfp-s-error .mfp-preloader {
	background: transparent;
	width: 100%;
	animation: none;
	white-space: nowrap;
	border: none;
	text-indent: 0;
	font-size: 17px;
	transition: none;
}
