// -------------------------------------------------------------------------------------------
// Avia AJAX Portfolio
// -------------------------------------------------------------------------------------------

(function($)
{
	"use strict";
	$.avia_utilities = $.avia_utilities || {};


	// -------------------------------------------------------------------------------------------
	//Portfolio sorting
	// -------------------------------------------------------------------------------------------

    $.fn.avia_iso_sort = function(options)
	{
		return this.each(function()
		{
			var the_body		= $('body'),
				container		= $(this),
				portfolio_id	= container.data('portfolio-id'),
				parentContainer	= container.closest('.av-portfolio-grid-sorting-container, .entry-content-wrapper, .avia-fullwidth-portfolio'),
				filter			= parentContainer.find('.sort_width_container[data-portfolio-id="' + portfolio_id + '"]').find('#js_sort_items').css({visibility:"visible", opacity:0}),
				links			= filter.find('a'),
				imgParent		= container.find('.grid-image'),
				isoActive		= false,
				items			= $('.post-entry', container),
				is_originLeft	= the_body.hasClass('rtl') ? false : true;

			function applyIso()
			{
				container.addClass('isotope_activated').isotope({
					layoutMode : 'fitRows', itemSelector : '.flex_column', originLeft: is_originLeft
				});

				container.isotope( 'on', 'layoutComplete', function()
				{
					container.css({overflow:'visible'});
					the_body.trigger('av_resize_finished');
				});

				isoActive = true;
				setTimeout(function(){ parentContainer.addClass('avia_sortable_active'); }, 0);
			};

			links.on('click',function()
			{
				var current		= $(this),
			  		selector	= current.data('filter'),
			  		linktext	= current.html(),
			  		activeCat	= parentContainer.find('.av-current-sort-title');

			  		if(activeCat.length) activeCat.html(linktext);

					links.removeClass('active_sort');
					current.addClass('active_sort');
					container.attr('id', 'grid_id_'+selector);

					parentContainer.find('.open_container .ajax_controlls .avia_close').trigger('click');
					//container.css({overflow:'hidden'})
					container.isotope({ layoutMode : 'fitRows', itemSelector : '.flex_column' , filter: '.'+selector, originLeft: is_originLeft });

					return false;
			});

			// update columnWidth on window resize
			$(window).on( 'debouncedresize', function()
			{
			  	applyIso();
			});

			$.avia_utilities.preload({container: container, single_callback:  function()
				{
					filter.animate({opacity:1}, 400); applyIso();

					//call a second time to for the initial resizing
					setTimeout(function(){ applyIso(); });

					imgParent.css({height:'auto'}).each(function(i)
					{
						var currentLink = $(this);

						setTimeout(function()
						{
							currentLink.animate({opacity:1},1500);
						}, (100 * i));
					});
				}
			});

		});
	};





	$.fn.avia_portfolio_preview = function(passed_options)
	{
		var win  = $(window),
		the_body = $('body'),
		isMobile = $.avia_utilities.isMobile,
		defaults =
		{
			open_in:	'.portfolio-details-inner',
			easing:		'easeOutQuint',
			timing:		800,
			transition:	'slide' // 'fade' or 'slide'
		},

		options = $.extend({}, defaults, passed_options);

		return this.each(function()
		{
			var container			= $(this),
				portfolio_id		= container.data('portfolio-id'),
				target_wrap			= $('.portfolio_preview_container[data-portfolio-id="' + portfolio_id + '"]'),
				target_container	= target_wrap.find(options.open_in),
				items				= container.find('.grid-entry'),
				content_retrieved	= {},
				is_open				= false,
				animating			= false,
				index_open			= false,
				ajax_call			= false,
				methods,
				controls,
				loader				= $.avia_utilities.loading();

			methods =
			{
				load_item: function(e)
				{
					e.preventDefault();

					var link			= $(this),
						post_container	= link.parents('.post-entry').eq( 0 ),
						post_id			= "ID_" + post_container.data('ajax-id'),
						clickedIndex	= items.index(post_container);

					//check if current item is the clicked item or if we are currently animating
					if(post_id === is_open || animating == true)
					{
						return false;
					}

					animating = true;

					container.find('.active_portfolio_item').removeClass('active_portfolio_item');
					post_container.addClass('active_portfolio_item');
					loader.show();

					methods.ajax_get_contents(post_id, clickedIndex);
				},

				scroll_top: function()
				{
					setTimeout(function()
					{
						var target_offset = target_wrap.offset().top - 175,
							window_offset = win.scrollTop();

						if(window_offset > target_offset || target_offset - window_offset > 100  )
						{
							$('html:not(:animated),body:not(:animated)').animate({ scrollTop: target_offset }, options.timing, options.easing);
						}
					},10);
				},

				attach_item: function(post_id)
				{
					content_retrieved[post_id] = $(content_retrieved[post_id]).appendTo(target_container);
					ajax_call = true;
				},

				remove_video: function()
				{
					var del = target_wrap.find('iframe, .avia-video').parents('.ajax_slide:not(.open_slide)');

						if(del.length > 0)
						{
							del.remove();
							content_retrieved["ID_" + del.data('slideId')] = undefined;
						}
				},

				show_item: function(post_id, clickedIndex)
				{

					//check if current item is the clicked item or if we are currently animating
					if(post_id === is_open)
					{
						return false;
					}
					animating = true;


					loader.hide();

					if(false === is_open)
					{
						target_wrap.addClass('open_container');
						content_retrieved[post_id].addClass('open_slide');

						methods.scroll_top();

						target_wrap.css({display:'none'}).slideDown(options.timing, options.easing, function()
						{
							if(ajax_call)
							{
								$.avia_utilities.activate_shortcode_scripts(content_retrieved[post_id]);
								$.avia_utilities.avia_ajax_call(content_retrieved[post_id]);
								the_body.trigger('av_resize_finished');
								ajax_call = false;
							}

							methods.remove_video();
							the_body.trigger('av_resize_finished');
						});

							index_open	= clickedIndex;
							is_open		= post_id;
							animating	= false;



					}
					else
					{
						methods.scroll_top();

						var initCSS = { zIndex:3 },
							easing	= options.easing;

						if(index_open > clickedIndex) { initCSS.left = '-110%'; }
						if(options.transition === 'fade'){ initCSS.left = '0%'; initCSS.opacity = 0; easing = 'easeOutQuad'; }

						//fixate height for container during animation
						target_container.height(target_container.height()); //outerHeight = border problems?

						content_retrieved[post_id].css(initCSS).avia_animate({'left':"0%", opacity:1}, options.timing, easing);
						content_retrieved[is_open].avia_animate({opacity:0}, options.timing, easing, function()
						{
							content_retrieved[is_open].attr({'style':""}).removeClass('open_slide');
							content_retrieved[post_id].addClass('open_slide');
																										  //+ 2 fixes border problem (slides move up and down 2 px on transition)
							target_container.avia_animate({height: content_retrieved[post_id].outerHeight() + 2}, options.timing/2, options.easing, function()
							{
								target_container.attr({'style':""});
								is_open		= post_id;
								index_open	= clickedIndex;
								animating	= false;

								methods.remove_video();
								if(ajax_call)
								{
									the_body.trigger('av_resize_finished');
									$.avia_utilities.activate_shortcode_scripts(content_retrieved[post_id]);
									$.avia_utilities.avia_ajax_call(content_retrieved[post_id]);
									ajax_call = false;
								}

							});

						});
					}
				},

				ajax_get_contents: function(post_id, clickedIndex)
				{
					if(content_retrieved[post_id] !== undefined)
					{
						methods.show_item(post_id, clickedIndex);
						return;
					}

					/**
					 * Possible fix for complex pages that throw a js error when user clicks item and not fully loaded
					 */
					var template = $('#avia-tmpl-portfolio-preview-' + post_id.replace(/ID_/,""));
					if( template.length == 0 )
					{
						setTimeout( function(){ methods.ajax_get_contents( post_id, clickedIndex); return; }, 500);
					}

					content_retrieved[post_id] = template.html();

					//this line is necessary to prevent w3 total cache from messing up the portfolio if inline js is compressed
					content_retrieved[post_id] = content_retrieved[post_id].replace('/*<![CDATA[*/','').replace('*]]>','');

					methods.attach_item(post_id);

					$.avia_utilities.preload({container: content_retrieved[post_id] , single_callback:  function(){ methods.show_item(post_id, clickedIndex); }});
				},

				add_controls: function()
				{
					controls = target_wrap.find('.ajax_controlls');

					target_wrap.avia_keyboard_controls({27:'.avia_close', 37:'.ajax_previous', 39:'.ajax_next'});
					//target_wrap.avia_swipe_trigger({prev:'.ajax_previous', next:'.ajax_next'});

					items.each(function(){

						var current = $(this), overlay;

						current.addClass('no_combo').on('click', function(event)
						{
							overlay = current.find('.slideshow_overlay');

							if(overlay.length)
							{
								event.stopPropagation();
								methods.load_item.apply(current.find('a').eq( 0 ));
								return false;
							}
						});


					});
				},

				control_click: function()
				{
					var showItem,
						activeID = container.find('.active_portfolio_item').data('ajax-id'),
						active   = container.find('.post-entry-'+activeID);

					switch(this.hash)
					{
						case '#next':

							showItem = active.nextAll('.post-entry:visible').eq( 0 ).find('a').eq( 0 );
							if(!showItem.length) { showItem = $('.post-entry:visible', container).eq( 0 ).find('a').eq( 0 ); }
							showItem.trigger('click');

						break;
						case '#prev':

							showItem = active.prevAll('.post-entry:visible').eq( 0 ).find('a').eq( 0 );
							if(!showItem.length) { showItem = $('.post-entry:visible', container).last().find('a').eq( 0 ); }
							showItem.trigger('click');

						break;
						case '#close':

							animating = true;

							target_wrap.slideUp( options.timing, options.easing, function()
							{
								container.find('.active_portfolio_item').removeClass('active_portfolio_item');
								content_retrieved[is_open].attr({'style':""}).removeClass('open_slide');
								target_wrap.removeClass('open_container');
								animating = is_open = index_open = false;
								methods.remove_video();
								the_body.trigger('av_resize_finished');
							});

						break;
					}
					return false;
				},

				resize_reset: function()
				{
					if(is_open === false)
					{
						target_container.html('');
						content_retrieved = [];
					}
				}
			};

			methods.add_controls();

			container.on("click", "a", methods.load_item);
			controls.on("click", "a", methods.control_click);

			//	removed in 4.8 as jQuery.support was deprecated with 2.0 and returns "undefined"/false
//			if(jQuery.support.leadingWhitespace) { win.on('debouncedresize', methods.resize_reset); }
			win.on('debouncedresize', methods.resize_reset);

		});
	};
}(jQuery));


