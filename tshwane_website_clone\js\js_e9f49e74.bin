// -------------------------------------------------------------------------------------------
// AVIA Image Hotspots
// -------------------------------------------------------------------------------------------

(function($)
{
	"use strict";

	$.fn.aviaHotspots = function( options )
	{
		if( ! this.length )
		{
			return;
		}

		return this.each(function()
		{
			var _self = {};

			_self.container	= $(this);
			_self.hotspots	= _self.container.find('.av-image-hotspot');

				_self.container.on('avia_start_animation', function()
				{
					setTimeout(function()
					{
						_self.hotspots.each(function(i)
						{
							var current = $(this);
							setTimeout(function(){ current.addClass('av-display-hotspot'); },300 * i);
						});
					},400);
				});

		});
	};

}(jQuery));