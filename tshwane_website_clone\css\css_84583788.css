.av-icon-circles-container{
	width: 100%;
	padding-bottom: 100%;
	height: 0px;
	clear: both;
	position: relative;
}

.avia-icon-circles-main-logo{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 12;
}

.av-icon-circles-container.active-container .avia-icon-circles-main-logo{
	opacity: 0;
}

.avia-icon-circles-main-logo img{
	max-width: 60%;
	max-height: 60%;
}

.avia-icon-circles-icon{
	border-width: 1px;
	border-style: solid;
	border-radius: 100px;
	height: 60px;
	width: 60px;
	margin: -30px;
	position: absolute;
	text-align: center;
	line-height: 58px;
	font-size: 25px;
	z-index: 5;
}

.avia-icon-circles-icon.av-linked-icon{
	cursor: pointer;
}

.avia-icon-circles-icon.av-linked-icon:hover{
	text-decoration: none;
}

.avia-icon-circles-inner{
	top: 0;
	left: 0;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 11;
	border-width: 2px;
	border-style: solid;
	border-radius: 10000px;
	transform: scale(0.1);
	border-color: #000;
	opacity: 0;
}

.avia-icon-circles-icon{
	opacity: 0;
}

.avia_start_animation .avia-icon-circles-inner{
	transition: all 0.8s cubic-bezier(0.175, 0.885, 0.320, 1.275);
	transform: scale(1);
	border-color: #e1e1e1;
	opacity: 1;
}

.avia_start_animation.avia_animation_finished .avia-icon-circles-icon{
	transition-delay: 0s;
	z-index: 20;
}

.avia_animation_finished .avia-icon-circles-inner{
	z-index: 20;
}

.avia-icon-circles-icon-text{
	transition: all 0.8s cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 0;
	z-index: 12;
	display: table;
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 1000px;
	padding: 10%;
	text-align: center;
	-webkit-backface-visibility: hidden;
}

.avia-icon-circles-icon-text-inner{
	display: table-cell;
	vertical-align: middle;
}

.avia-icon-circles-icon-text .icon-title{
	letter-spacing: 1px;
	font-size: 21px;
	font-weight: normal;
	margin-bottom: 6px;
	display: block;
	line-height: 1.3em;
}

.avia-icon-circles-icon-text .icon-description{
	line-height: 1.1;
}

.avia_start_animation .avia-icon-circles-icon{
	transition: all 0.4s 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275);
}

.active.avia-icon-circles-icon-text{
	z-index: 13;
	opacity: 0.98;
}

#av-admin-preview .avia-icon-circles-icon-text.av-hide-989 .icon-description{
	display: block;
}
