/* ======================================================================================================================================================
GENERIC
====================================================================================================================================================== */


p:empty,
.entry-content-wrapper:empty{
	display:none;
}

.avia-shadow{
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/*fix webkit flickering bug*/
.webkit_fix{
	-webkit-perspective: 1000px;
	-webkit-backface-visibility: hidden;
}

body .container_wrap .avia-builder-el-no-sibling{
	margin-top:0;
	margin-bottom:0;
}

body .container_wrap .avia-builder-el-last{
	margin-bottom:0;
}

body .container_wrap .avia-builder-el-first{
	margin-top:0;
}

body .av-align-left{
	text-align: left;
}

body .av-align-right{
	text-align: right;
}

body .av-align-center{
	text-align: center;
}

#top .av_inherit_color *{
	color:inherit;
}
#top .av_inherit_color a{
	text-decoration: underline;
}

#top .av_opacity_variation{
	opacity: 0.7;
}

#top .av-shortcode-disabled-notice{
	display:block;
	text-align: center;
	margin: 5px;
	padding: 20px;
	background-color: #FFFDF3;
	color: #C96D11;
	border:1px solid #E6BF4A;
	clear:both;
}

#top .av-shortcode-disabled-notice a{
	color: inherit;
	text-decoration: underline;
}

/* ======================================================================================================================================================
SCREEN READER see https://webaim.org/techniques/css/invisiblecontent/#techniques
====================================================================================================================================================== */
.av-screen-reader-only {
	position: absolute;
	left: -10000px;
	top: auto;
	width: 1px;
	height: 1px;
	overflow: hidden;
}

/*	=====================================================================================
Loading icon e.g. masonry, portfolio     (moved from portfolio.css)
========================================================================================== */
.avia_loading_icon{
	background: rgba(0,0,0,0.7);
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	margin: 0;
	display: none;
	z-index: 10000;
}

#top .avia_loading_icon .av-siteloader{
	border-top-color: #fff;
	border-right-color: #fff;
	border-bottom-color: #fff;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -20px 0 0 -20px;
}

/*	=============================================================================================
Tooltip for social share buttons     (moved from blog.css - breaks layout if blog disabled )
================================================================================================= */
.avia-related-tooltip{
	position: absolute;
	z-index: 9999999;
	padding: 0;
	width: 200px;
	border-radius: 2px;
	box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.1);
	border-width: 1px;
	border-style: solid;
	padding: 15px;
	margin-top: -10px;
}

.avia-related-tooltip .avia-arrow-wrap{
	top: auto;
	bottom: 0;
	position: absolute;
	left: 50%
}

.avia-related-tooltip .avia-arrow-wrap .avia-arrow{
	border-top: none;
	border-left: none;
	top: -4px;
}


/* ======================================================================================================================================================
TEXT BLOCK
====================================================================================================================================================== */
.avia_textblock{
	clear: both;
	line-height: 1.65em;
}

.avia_textblock.av_multi_colums > p:first-child{
	margin-top: 0;
}

.flex_column + .avia_textblock{
	padding-top: 50px;
}

/* ======================================================================================================================================================
Columns
====================================================================================================================================================== */
body .column-top-margin{
	margin-top:50px;
}

body .flex_column.avia-link-column{
	cursor: pointer;
}
body .flex_column.avia-link-column.avia-link-column-hover:hover{
	opacity: 0.8;
}

/* ======================================================================================================================================================
Icon
====================================================================================================================================================== */
.av-icon-display{
	display: block;
	font-size: 30px;
	line-height: 58px;
	margin: 0 auto 20px auto;
	width: 60px;
	border-radius: 100px;
	border-style: solid;
	border-width: 1px;
	text-align: center;
	transition: all 0.3s ease-in-out;
}

/* ======================================================================================================================================================
COLOR SECTION
====================================================================================================================================================== */
.avia-section{
	clear:both;
	width:100%;
	float:left;
	min-height:100px;
	position: static; /*fixes a glitch with bg image not displaying below video slide*/
}

.avia-section.avia-section-small{
	min-height:0;
}

.js_active .av-minimum-height .container{
	z-index: 1; /*required for transition*/
	opacity: 0;
	transition: opacity 0.6s ease-in;
}

.js_active.av-preloader-enabled .av-minimum-height .container{
	transition: opacity 1.2s ease-in;
}

.avia-section.av-minimum-height .container{
	display: table;
	table-layout: fixed;
}

.avia-section.av-minimum-height .container .content{
	display: table-cell;
	vertical-align: middle;
	height:100%;
	float:none !important;
}

.av-minimum-height-100 .container {
	height:1500px;			/*will be overwritten by js*/
}

.avia-full-stretch{
	background-size: cover !important;
}

.avia-full-contain{
	background-size: contain !important;
}

.avia_mobile .avia-full-stretch{
	background-attachment: scroll !important;
}

#top #main .avia-section .template-page{
	width:100%;
	border:none;
	margin-left: auto;
	padding-left: 0;
	margin-right: auto;
}

.avia-section .template-page .entry-content-wrapper{
	padding-right:0;
}

.avia-section-small .content,
.avia-section-small .sidebar {
	padding-top: 20px;
	padding-bottom: 20px;
}

.avia-section-large .content,
.avia-section-large .sidebar {
	padding-top: 70px;
	padding-bottom: 70px;
}

.avia-section-huge .content,
.avia-section-huge .sidebar {
	padding-top: 130px;
	padding-bottom: 130px;
}

.avia-section-no-padding .content,
.avia-section-no-padding .sidebar,
.avia-section-custom .content,
.avia-section-custom .sidebar{
	padding-top:0;
	padding-bottom:0;
}

.html_header_sticky_disabled .container_wrap:first-child,
.avia-section.avia-full-stretch,
.html_header_transparency .avia-section.avia-builder-el-0{
	border-top:none;
}

#top #wrap_all #main .avia-no-border-styling{
	border:none;
}

#top .scroll-down-link {
	height: 60px;
	width: 80px;
	margin: 0px 0 0 -40px;
	line-height: 60px;
	position: absolute;
	left: 49.85%;
	bottom: 0px;
	color: #FFF;
	text-align: center;
	font-size: 70px;
	z-index: 100;
	text-decoration: none;
	text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.4);
	animation: avia_fade_move_down 2s ease-in-out infinite;
}

/*video bg*/
#top .av-section-with-video-bg{
	border-top:none;
	position: relative;
}

#top .av-section-video-bg{
	position: absolute;
	top:0;
	left:0;
	right:0;
	bottom: 0;
	padding: 0;
	margin: 0;
	z-index: 1;
}

#top .av-section-video-bg .av-click-to-play-overlay{
	display: none;
}

#top .av-section-video-bg .avia-slideshow-inner{
	height:100% !important;
}

#top .av-section-with-video-bg .av-section-video-bg .av-video-service-vimeo iframe{
	transform: 			scale(1.2);
}

/*handle video section on mobile*/
.avia_mobile #top .av-parallax-section{
	z-index: 0;
	transform: translate3d(0,0,0);
}

.avia_mobile #top .av-parallax{
	position: absolute;
	z-index: -10;
}

.avia_mobile #top .av-parallax .container{
	z-index: 10;
}
.avia_mobile #top .av-section-mobile-video-disabled .av-section-video-bg{
	display:none;
}

.avia_desktop #top .av-section-mobile-video-disabled
{background-image: none !important;
}

.avia_desktop #top .av-section-mobile-video-disabled .av-parallax{
	display: none;
}

@media only screen and (max-width: 1024px) {
    .touch-device #top .avia-section.avia-bg-style-fixed {
        background-attachment: scroll;
    }
}


/*parallax section*/
.av-parallax-section{
	position: relative;
	border-top: none;
	overflow: hidden;
}

.av-parallax {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
	height: calc((100vh * 0.3) + 100%);
	will-change: transform;
}

.av-parallax-inner{
	position: absolute;
	width: 100%;
	height:100%;
	bottom:0;
	left:0;
}

/*prevent img pos flickering*/
.js_active .av-parallax,
.js_active .av-parallax-object{
	opacity: 0;
}

.js_active .av-parallax.enabled-parallax{
	opacity: 1
}

.js_active .av-parallax.active-parallax,
.js_active .av-parallax-object.active-parallax{
	opacity: 1;
	transition: opacity 0.3s ease-in-out;
}


/*special border top elements*/
.avia_transform .av-extra-border-element{
	position: relative;
	width: 100%;
	overflow: visible;
}

.avia_transform .av-extra-border-element.border-extra-arrow-down{
	position: relative;
	width: 50px;
	height: 25px;
	left: 50%;
	margin-left: -26px;
	margin-top: -1px;
	z-index: 25;
	margin-bottom: -25px;
	clear:both;
}

.avia_transform .av-extra-border-element .av-extra-border-outer{
	overflow: hidden;
	height:100%;
	width:100%;
	position: absolute;
}

.avia_transform .av-extra-border-element.border-extra-arrow-down .av-extra-border-inner{
	position: absolute;
	left: 50%;
	width: 50px;
	height: 50px;
	transform: translateX(-50%) rotate(45deg);
	top: -39px;
}

.av-section-color-overlay + .container + .border-extra-diagonal,
.av-section-color-overlay + .scroll-down-link + .container + .border-extra-diagonal{
    z-index: 1;
}

.avia_transform .av-extra-border-element.border-extra-diagonal{
	height:250px;
	margin-top: -250px;
}

.avia_transform .av-extra-border-element.border-extra-diagonal .av-extra-border-inner{
	top: 250px;
    right: 0;
    width: 110%;
    height: 100%;
    transform-origin: right top;
    transform: rotate(5deg);
    position: absolute;
}

.avia_transform .av-extra-border-element.border-extra-diagonal.border-extra-diagonal-inverse .av-extra-border-inner{
    transform: rotate(-5deg);
    transform-origin: left top;
    right:auto;
    left:0;
}

.av-extra-border-element.diagonal-box-shadow .av-extra-border-inner{
    box-shadow: 0 0 44px -8px rgba(0,0,0,0.2);
}

div .av-section-color-overlay-wrap{
	position: relative;
	width:100%;
}

.html_header_sidebar .av-section-color-overlay-wrap{
	float:left;
}

div .av-section-color-overlay{
	position: absolute;
	top:0;
	left: 0;
	bottom: 0;
	width:100%;
}

div .av-arrow-down-section .av-section-color-overlay{
	bottom: 24px;
}

div .av-section-color-overlay{
	z-index: 1;
}

div .av-video-service-html5 .av-section-color-overlay{
	z-index: 8;
}

.html_minimal_header #main > .avia-builder-el-0,
.av_minimal_header + #main .container_wrap_first:first-child {
	border-top: none;
}


/* ======================================================================================================================================================
Dropcaps
====================================================================================================================================================== */
.av_dropcap1 {
	display: block;
	float: left;
	font-size: 38px;
	line-height: 32px;
	margin: 4px 8px 0 0;
}

.av_dropcap2{
	display:block;
	float:left;
	font-size:18px;
	line-height:35px;
	width:35px;
	text-align: center;
	margin:4px 8px 0 0;
	border-radius: 100px;
	font-weight: bold;
}

/* ======================================================================================================================================================
Contact Form Defaults
====================================================================================================================================================== */

fieldset{
	border:none;
}

#top select,
#top .avia_ajax_form .select,
#top .entry-content-wrapper select{
	-webkit-appearance: none;
	border-radius:0px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAANCAYAAAC+ct6XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RjBBRUQ1QTQ1QzkxMTFFMDlDNDdEQzgyNUE1RjI4MTEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RjBBRUQ1QTU1QzkxMTFFMDlDNDdEQzgyNUE1RjI4MTEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGMEFFRDVBMjVDOTExMUUwOUM0N0RDODI1QTVGMjgxMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGMEFFRDVBMzVDOTExMUUwOUM0N0RDODI1QTVGMjgxMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pk5mU4QAAACUSURBVHjaYmRgYJD6////MwY6AyaGAQIspCieM2cOjKkIxCFA3A0TSElJoZ3FUCANxAeAWA6IOYG4iR5BjWwpCDQCcSnNgxoIVJCDFwnwA/FHWlp8EIpHSKoGgiggLkITewrEcbQO6mVAbAbE+VD+a3IsJTc7FQAxDxD7AbEzEF+jR1DDywtoCr9DbhwzDlRZDRBgACYqHJO9bkklAAAAAElFTkSuQmCC);
	background-position: center right;
	background-repeat: no-repeat;
	border-radius: 2px;
}

#top select[multiple],
#top .avia_ajax_form .select[multiple]{
	background-image:none;
}

#top select[multiple] {
	background-image:none;
}


/* ======================================================================================================================================================
Sidebar
====================================================================================================================================================== */
.avia-builder-widget-area{
	position: relative;
}

.flex_column .widget:first-child,
.content .widget:first-child{
	padding-top: 0;
	border-top: none;
}

.flex_column .widget .widgettitle ,
.content .widget .widgettitle{
	margin-top:0.85em;
}



/* ======================================================================================================================================================
AVIA PLAYER Defaults
====================================================================================================================================================== */

/*playlist shortcode*/

.avia_textblock .wp-playlist{
	margin:0;
}

.wp-playlist-light .wp-playlist-item.wp-playlist-playing{
	background: transparent;
}

.wp-playlist-current-item img{
	border-radius: 3px;
}

.wp-playlist-current-item img[src$="media/audio.png"]{
	border-radius: 0px;
	background: #fff;
}

.wp-playlist-item-meta.wp-playlist-item-title{
	font-weight: bold;
}

.wp-playlist-tracks .wp-playlist-item{
	padding: 4px 3px;
}

.wp-playlist-tracks .wp-playlist-item-length{
	top: 4px;
}

div .mejs-controls .mejs-time-rail .mejs-time-loaded,
div .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-current{
	background:#9c9c9c;
}

div .mejs-controls .mejs-time-rail > span,
div .mejs-controls .mejs-time-rail a,
div .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-current,
div .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-total{
	height:7px;
}

div .mejs-controls div.mejs-time-rail{
	padding-top: 7px;
}

div .mejs-controls .mejs-horizontal-volume-slider .mejs-horizontal-volume-total{
	top:12px;
}

div .mejs-container,
div .mejs-embed,
div .mejs-embed body,
div .mejs-container div .mejs-controls{
	background:transparent;
}

div .mejs-container{
	border-radius:2px;
	background:#313131
}

div .wp-playlist-item-album{
	font-style: normal;
}

div .mejs-controls .mejs-playpause-button,
div .mejs-controls .mejs-volume-button{
	height:30px;
}

#top .mejs-container .mejs-controls .mejs-time,
#top .mejs-time-rail .mejs-time-float{
	color:#fff;
}

#top .mejs-time-rail .mejs-time-float {
	border:none;
}

div .mejs-container .mejs-controls{
	height: 30px;
	width: 100%;
}

#top .mejs-container{
	height: 30px;
}

#top .mejs-container.mejs-audio{
	max-height: 30px
}

div .mejs-button>button {
	margin: 6px 6px;
}

div .mejs-time{
	padding: 12px 6px 0;
	height:18px;
}

div .mejs-time-rail,
div .mejs-horizontal-volume-slider{
	height:30px;
}

div .mejs-controls .mejs-time-rail .mejs-time-handle{
	display:none;
}

div .mejs-time-buffering,
div .mejs-time-current,
div .mejs-time-float-corner,
div .mejs-time-hovered,
div .mejs-time-loaded,
div .mejs-time-marker,
div .mejs-time-total{
	height:7px;
	outline:none;
}

div .mejs-controls{
	padding:0;
}


/* ======================================================================================================================================================
Privacy toggles frontend
====================================================================================================================================================== */
#top .av-toggle-switch{
	display: block;
	margin-bottom: 10px;
	margin-top:10px;
	opacity: 0;
	text-align: left;
}

#top .av-toggle-switch.active{
	animation: avia-fadein 1s 1 ease-in;
	opacity: 1;
}

#top .av-toggle-switch input[type="checkbox"] {
	display:none
}

#top .av-toggle-switch label {
	cursor:pointer;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

#top .av-toggle-switch label .toggle-track {
	display:block;
	height:27px;
	width:54px;
	background:rgba(0, 0, 0, 0.08);
	border-radius:100px;
	position:relative;
	margin-right:15px;
	border:1px solid rgba(0, 0, 0, 0.15);
	clear:both;
	vertical-align: bottom;
	float: left;
	transition:all .1s ease-in;
}

#top .av-toggle-switch .toggle-track:before{
	content:'';
	display:inline;height:25px;width:25px;background:#fff;
	border-radius:100px;
	position:absolute;
	top:0;
	right:0;
	transition:right .1s ease-in;
	box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 1px 1px;
}

#top .av-toggle-switch.av-cookie-sc-disabled .toggle-track{
	opacity: 0.2;
}

#top .av-toggle-switch input[type="checkbox"]:checked +  .toggle-track:before{
	right:27px;
}

#top .av-toggle-switch input[type="checkbox"]:checked +  .toggle-track{
	background:#5dceba;
}

#top .av-toggle-switch .toggle-label-content{
	color: inherit;
	font-weight: bold;
	display: block;
	overflow: hidden;
	font-size: inherit;
}

#top .avia-cookie-consent-modal-buttons-wrap{
	text-align: center;
}


/* ======================================================================================================================================================
Tooltips
========================================================================================================================================================= */
.avia-tooltip{
	position: absolute;
	z-index: 9999999;
	padding:12px;
	margin-top:-18px;
	margin-left: -4px;
	background: #000;
	background: rgba(0, 0, 0, 0.8);
	color:#fff;
	width:140px;
	border-radius: 2px;
	text-align: center;
}

.avia-tooltip .inner_tooltip{
	font-size: 0.925em;
	line-height: 1.65em;
}

#top .avia-tooltip.transparent_dark,
#top .avia-tooltip.transparent_dark .avia-arrow{
	background: #000;
	background: rgba(0, 0, 0, 0.8);
	color:#fff;
	border:none;
}

#top .avia-tooltip.transparent_dark h1,
#top .avia-tooltip.transparent_dark h2,
#top .avia-tooltip.transparent_dark h3,
#top .avia-tooltip.transparent_dark h4,
#top .avia-tooltip.transparent_dark h5,
#top .avia-tooltip.transparent_dark h6{
	color:inherit;
}

.avia-tooltip.av-tooltip-shadow,
.avia-tooltip.av-tooltip-shadow .avia-arrow{
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
}

.avia-tooltip ul,
.avia-tooltip ol,
.avia-tooltip blockquote{
	text-align: left;
	list-style-position: inside;
}

.avia-tooltip .avia-arrow{
	background: rgba(0, 0, 0, 0.8);
	border:none;
}

.avia-tooltip .avia-arrow-wrap{
	width:20px;
	height:20px;
	position: absolute;
	bottom:-20px;
	left:50%;
	margin-left:-7px;
	overflow: hidden;
}

.avia-tooltip .inner_tooltip br:first-child{
	display:none;
}

/*tooltip positions. mainly used for image hotspots but also for testimonials*/
.av-tt-hotspot .avia-arrow-wrap{
	bottom:auto;
	bottom:-19px;
}

.transparent_dark.av-tt-hotspot .avia-arrow-wrap{
	bottom:-20px;
}

/*below*/
.avia-tooltip.av-tt-pos-below{
	margin-top: 18px;
}

.avia-tooltip.av-tt-pos-below .avia-arrow-wrap{
	bottom:auto;
	top:-19px;
}

.avia-tooltip.av-tt-pos-below .avia-arrow{
	bottom: -6px;
	top:auto;
}

/*to the left*/
.avia-tooltip.av-tt-pos-left{
	margin-top: 0;
	margin-left: -18px;
}

.avia-tooltip.av-tt-pos-left .avia-arrow-wrap{
	left: auto;
	margin-left: 0;
	right: -19px;
	top: 50%;
	margin-top: -10px;
}

.avia-tooltip.av-tt-pos-left .avia-arrow{
	top: 5px;
	left: -5px;
	margin-left: 0;
}

/*to the right*/
.avia-tooltip.av-tt-pos-right{
	margin-top: 0;
	margin-left: 18px;
}

.avia-tooltip.av-tt-pos-right .avia-arrow-wrap{
	left: -19px;
	margin-left: 0;
	top: 50%;
	margin-top: -10px;
}

.avia-tooltip.av-tt-pos-right .avia-arrow{
	top: 5px;
	left:auto;
	right: -5px;
	margin-left: 0;
}

/*arrow alignment for above and below tooltips*/
.av-tt-pos-above.av-tt-align-right.avia-tooltip,
.av-tt-pos-below.av-tt-align-right.avia-tooltip{
	margin-left:4px;
}

.av-tt-pos-above.av-tt-align-left .avia-arrow-wrap,
.av-tt-pos-below.av-tt-align-left .avia-arrow-wrap{
	left: 0;
	margin-left: 7px;
}

.av-tt-pos-above.av-tt-align-right .avia-arrow-wrap,
.av-tt-pos-below.av-tt-align-right .avia-arrow-wrap{
	left: auto;
	right:0;
	margin-left: auto;
	margin-right: 7px;
}

/*arrow alignment for left and right tooltips*/
.av-tt-pos-left.av-tt-align-top .avia-arrow-wrap,
.av-tt-pos-right.av-tt-align-top .avia-arrow-wrap{
	top: 0;
	margin-top: 2px;
}

.av-tt-pos-left.av-tt-align-bottom .avia-arrow-wrap,
.av-tt-pos-right.av-tt-align-bottom .avia-arrow-wrap{
	bottom: 0;
	margin-bottom: 4px;
	top: auto;
}

.av-tt-pos-above.av-tt-align-centered,
.av-tt-pos-below.av-tt-align-centered{
	text-align: center;
}

/* ======================================================================================================================================================
ANIMATIONS
====================================================================================================================================================== */
.avia_transform .av-animated-generic {
	opacity: 0;
}

.avia_transform.avia_mobile .avia-mobile-no-animations .av-animated-generic {
	opacity: 1;
}

.avia_transform .av-animated-when-visible,
.avia_transform .av-animated-when-almost-visible,
.avia_transform .av-animated-when-visible-95{
	opacity: 0;
}

.avia_transform .shadow-animated.av-animated-when-visible,
.avia_transform .shadow-animated.av-animated-when-almost-visible,
.avia_transform .shadow-animated.av-animated-when-visible-95{
	opacity: 1;
}

.avia-animate-admin-preview.fade-in,
.avia_transform .avia_start_delayed_animation.fade-in {
	animation: avia-fadein 1.5s 1 ease-out;
	opacity: 1;
}

.avia-animate-admin-preview.pop-up,
.avia_transform .avia_start_delayed_animation.pop-up {
	animation: avia_image_appear 0.5s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.top-to-bottom,
.avia_transform .avia_start_delayed_animation.top-to-bottom {
	animation: avia-ttb 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.bottom-to-top,
.avia_transform .avia_start_delayed_animation.bottom-to-top {
	animation: avia-btt 0.8s 1 cubic-bezier(0.165, 0.840, 0.440, 1.000);
	opacity: 1;
}

.avia-animate-admin-preview.left-to-right,
.avia_transform .avia_start_delayed_animation.left-to-right {
	animation: avia-ltr 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.right-to-left,
.avia_transform .avia_start_delayed_animation.right-to-left {
	animation: avia-rtl 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.av-rotateIn,
.avia_transform .avia_start_delayed_animation.av-rotateIn {
	animation: avia-rotateIn 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.av-rotateInUpLeft,
.avia_transform .avia_start_delayed_animation.av-rotateInUpLeft {
	animation: avia-rotateInUpLeft 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.av-rotateInUpRight,
.avia_transform .avia_start_delayed_animation.av-rotateInUpRight {
	animation: avia-rotateInUpRight 0.8s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
}

.avia-animate-admin-preview.avia-curtain-reveal-overlay,
.avia_transform.avia_desktop .avia-curtain-reveal-overlay,
.avia_transform .avia-curtain-reveal-overlay{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 1;
	z-index: 100;
}

.avia-animate-admin-preview.curtain-reveal-ttb,
.avia_transform .avia_start_delayed_animation.curtain-reveal-ttb{
	animation: avia-curtain-reveal-ttb cubic-bezier(0.17,0.84,0.44,1) 1s 1;
	height: 0;
	top: 100%;
}

.avia-animate-admin-preview.curtain-reveal-btt,
.avia_transform .avia_start_delayed_animation.curtain-reveal-btt{
	animation: avia-curtain-reveal-btt cubic-bezier(0.17,0.84,0.44,1) 1s 1;
	height: 0;
}

.avia-animate-admin-preview.curtain-reveal-ltr,
.avia_transform .avia_start_delayed_animation.curtain-reveal-ltr{
	animation: avia-curtain-reveal-ltr cubic-bezier(0.17,0.84,0.44,1) 1s 1;
	width: 0;
	left: 100%;
}

.avia-animate-admin-preview.curtain-reveal-rtl,
.avia_transform .avia_start_delayed_animation.curtain-reveal-rtl{
	animation: avia-curtain-reveal-rtl cubic-bezier(0.17,0.84,0.44,1) 1s 1; /* IE 10+ */
	width: 0;
}


/*pop up animation*/
@keyframes avia_appear {
  0%   { transform:scale(0.5); opacity: 0.1;  }
  100% { transform:scale(1); opacity: 1;  }
}

@keyframes avia_image_appear {
  0%   { transform:scale(0.7); opacity: 0.1;  }
  100% { transform:scale(1); opacity: 1;  }
}

@keyframes avia_hotspot_appear {
  0%   { transform:translate(0, 80px); opacity: 0.1;  }
  100% { transform:translate(0, 0px); opacity: 1;  }
}

@keyframes avia_appear_short {
  0%   { transform:scale(0.5); opacity: 0;  }
  50%  { opacity: 1;  }
  100% { transform:scale(1); opacity: 0;  }
}


/*pop up animation*/
@keyframes avia_masonry_show {
  0%   { transform:translateZ(300px) translateY(200px) rotateX(-70deg); opacity: 0.1;  }
  100% { transform:translateZ(0px) translateY(0px) rotateX(0deg); opacity: 1;  }
}


/*animated arrow animattion*/
@keyframes avia_fade_move_down {
  0%   { transform:translate(0,-20px); opacity: 0;  }
  50%  { opacity: 1;  }
  100% { transform:translate(0,20px); opacity: 0; }
}


/*slide down (height increase) animation*/
@keyframes avia_slide_down {
  0%   { height:0%; }
  100% { height:100%; }
}


/*expand width animation*/
@keyframes avia_expand {
  0%   { width:0%; }
  100% { width:100%; }
}


/*fade in animation*/
@keyframes avia-ltr {
  0%   { transform:translate(-10%,0); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes avia-rtl {
  0%   { transform:translate(10%,0); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes avia-btt {
  0%   { transform:translate(0,50%); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes avia-ttb {
  0%   { transform:translate(0, -10%); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}


@keyframes avia-fadein {
  0%   { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes avia-fadeOut {
  0%   { opacity: 1; }
  100% { opacity: 0; }
}


/*rotate*/
@keyframes avia-rotateIn {
  0% {
    transform-origin: center;
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }

  100% {
    transform-origin: center;
    transform: none;
    opacity: 1;
  }
}

@keyframes avia-rotateInUpLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  100% {
    transform-origin: left bottom;
    transform: none;
    opacity: 1;
  }
}

@keyframes avia-rotateInUpRight {
  0% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, -22deg);
    opacity: 0;
  }

  100% {
    transform-origin: right bottom;
    transform: none;
    opacity: 1;
  }
}


/*background animation*/
@keyframes avia-bg-move {
  0%   { background-position: -31px 0;  }
  100% { background-position: 0 0; }
}


/*caption animation*/
@keyframes caption-right {
  0%   { transform:translate(2000px,0); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes caption-left {
  0%   { transform:translate(-2000px,0); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes caption-top {
  0%   { transform:translate(0,-20px); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}

@keyframes caption-bottom {
  0%   { transform:translate(0,20px); opacity: 0;  }
  100% { transform:translate(0,0); opacity: 1; }
}


/*image hotspot pulse*/
@keyframes av_pulsate {
    0% 		{transform: scale(0.1); opacity: 0.0;}
    50% 	{opacity: 0.7;}
    100% 	{transform: scale(1); opacity: 0.0;}
}


/*sonar effect*/
@keyframes sonarEffect {
	0% {opacity: 0.3;}
	40% {opacity: 0.5;box-shadow: 0 0 0 2px rgba(255,255,255,0.1), 0 0 10px 10px #fff, 0 0 0 10px rgba(255,255,255,0.5);}
	100% {box-shadow: 0 0 0 2px rgba(255,255,255,0.1), 0 0 10px 10px #fff, 0 0 0 10px rgba(255,255,255,0.5);transform: scale(1.5);opacity: 0;}
}


/* curtain reveals  1px miscalc on several screen sizes !!! */
@keyframes avia-curtain-reveal-ltr{
	0%	{ width: 101%; left: 0; }
	100%{ width: 0; left: 100%; }
}

@keyframes avia-curtain-reveal-rtl{
	0%	{ width: 100%; }
	100%{ width: 0; }
}

@keyframes avia-curtain-reveal-ttb{
	0%	{ height: 101%; top: 0; }
	100%{ height: 0; top: 100%; }
}

@keyframes avia-curtain-reveal-btt{
	0%	{ height: 100%; }
	100%{ height: 0; }
}

/*	==================================================================
	Advanced Animations		based on https://github.com/animate-css
	==================================================================
*/

.avia-animate-admin-preview.fade-in-left,
.avia_transform .avia_start_delayed_animation.fade-in-left{
	animation: avia-fade-in-left cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.fade-in-right,
.avia_transform .avia_start_delayed_animation.fade-in-right{
	animation: avia-fade-in-right cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.fade-in-down,
.avia_transform .avia_start_delayed_animation.fade-in-down{
	animation: avia-fade-in-down cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.fade-in-up,
.avia_transform .avia_start_delayed_animation.fade-in-up{
	animation: avia-fade-in-up cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.flip-in-x,
.avia_transform .avia_start_delayed_animation.flip-in-x{
	animation: avia-flip-in-x cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.flip-in-y,
.avia_transform .avia_start_delayed_animation.flip-in-y{
	animation: avia-flip-in-y cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.roll-in,
.avia_transform .avia_start_delayed_animation.roll-in{
	animation: avia-roll-in cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

.avia-animate-admin-preview.zoom-in,
.avia_transform .avia_start_delayed_animation.zoom-in{
	animation: avia-zoom-in cubic-bezier(0.17,0.84,0.44,1) 1s 1 forwards;
}

@keyframes avia-fade-in-left{
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0);
	}
  100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes avia-fade-in-right{
	0% {
		opacity: 0;
		transform: translate3d(100%, 0, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes avia-fade-in-down {
	0% {
		opacity: 0;
		transform: translate3d(0, -100%, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes avia-fade-in-up {
	0% {
		opacity: 0;
		transform: translate3d(0, 100%, 0);
	}
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes avia-flip-in-x{
	0% {
		transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
		animation-timing-function: ease-in;
		opacity: 0;
	}
/*	40% {
		transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
		animation-timing-function: ease-in;
	}
	60% {
		transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
		opacity: 1;
	}*/
	80% {
		transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
	}
	100% {
		transform: perspective(400px);
		opacity: 1;
	}
}

@keyframes avia-flip-in-y{
	0% {
		transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
		animation-timing-function: ease-in;
		opacity: 0;
	}
/*	40% {
		transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
		animation-timing-function: ease-in;
	}
	60% {
		transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
		opacity: 1;
	}*/
	80% {
		transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
	}
	100% {
		transform: perspective(400px);
		opacity: 1;
	}
}

@keyframes avia-roll-in{
	0% {
		opacity: 0;
		transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
	}
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

@keyframes avia-zoom-in{
	0% {
		opacity: 0;
		transform: scale3d(0.3, 0.3, 0.3);
	}
	100% {
		opacity: 1;
	}
}



/*	=============================
	Shape SVG Dividers
	=============================
*/
.avia-divider-svg{
	overflow: hidden;
	position: absolute;
	left: 0;
	width: 100%;
	line-height: 0;
	direction: ltr;
	pointer-events: none;
}

.avia-divider-svg-top{
    top: 0px;
}

/* fix a small gap on different screen sizes - rotate(180deg) seems not calculate correctly  */
.avia-divider-svg-bottom{
	bottom: -2px;
}

/*	Temporary fixes for Chrome 85 issue. Can be removed in a future version of Chrome.	*/
.avia-divider-svg-top:not(.avia-svg-original) svg,
.avia-divider-svg-bottom:not(.avia-svg-negative) svg{
	z-index: -1;
}

.avia-divider-svg.avia-divider-svg-bottom.avia-svg-original,
.avia-divider-svg.avia-divider-svg-top.avia-svg-negative{
	transform: rotate(180deg);
}

.avia-divider-svg svg{
	fill: #fff;
	display: block;
	width: calc(100% + 1.3px);
	position: relative;
	left: 50%;
	transform: translateX(-50%);
}

.avia-divider-svg path{
	transform-origin: center;
	transform: rotateY(0deg);
}

.avia-divider-svg.avia-flipped-svg svg{
	transform: translateX(-50%) rotateY(180deg);
}

.avia-divider-svg.avia-to-front{
	z-index: 100;
}


/*	special case for paralax with overlay hiding svg */
.av-parallax-section.av-section-color-overlay-active .avia-divider-svg{
	z-index: 1;
}
.av-parallax-section.av-section-color-overlay-active .avia-divider-svg.avia-to-front{
	z-index: 100;
}

