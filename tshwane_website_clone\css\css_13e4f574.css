/* ======================================================================================================================================================
Countdown
====================================================================================================================================================== */

.av-countdown-timer{
	clear:both;
	position: relative;
	opacity: 0;
	margin:30px 0;
}

.av-countdown-timer .av-countdown-timer-finished{
	display: none;
}

.av-countdown-timer-inner{
	display: table;
	width: 100%;
	table-layout: fixed;
}

.av-countdown-timer-inner:hover {
	text-decoration: none;
}

.av-countdown-cell{
	display: table-cell;
}

.av-countdown-cell-inner{
	display: block;
	margin:1px 1px 0 0;
	padding:20px;
}

.av-countdown-time{
	display: block;
	font-size:40px;
	line-height: 1em;
	font-weight:100;
	color:inherit;
	text-decoration: none;
}

.av-countdown-time-label{
	display: block;
	text-transform: uppercase;
	color:inherit;
	overflow: hidden;
	text-overflow: ellipsis;
	-moz-hyphens: inherit;
	line-height: 1.65em;
}

.av-countdown-timer-title{
	display: block;
	font-size:30px;
	line-height: 1.3em;
	margin:0 0 15px 0;
	text-decoration: none;
	position: relative;
}

.av-countdown-timer-title:hover{
	text-decoration: none;
}

.av-countdown-timer-title-bottom{
	margin:15px 0 0 0;
}

.av-countdown-timer.av-countdown-active,
.av-countdown-timer.av-countdown-finished {
	opacity: 1;
}

.av-countdown-timer.av-finished-msg.av-countdown-finished .av-countdown-timer-inner{
	display: none;
}

.av-countdown-timer.av-finished-msg.av-countdown-finished .av-countdown-timer-finished{
	display: table;
	width: 100%;
}

/*styles*/
#top .av-default-style .av-countdown-cell-inner{
	border-style: solid;
	border-width: 1px;
	margin: 2px;
}

#top .av-trans-light-style .av-countdown-cell-inner{
	color: #fff;
	background: transparent;
	border: 3px solid #fff;
	margin: 2px;
}

#top .av-trans-light-style .av-countdown-cell-inner .av-countdown-time-label{
	color: #fff;
}

#top .av-trans-dark-style .av-countdown-cell-inner{
	color: #000;
	background: transparent;
	border: 3px solid #000;
	margin: 2px;
}

#top .av-trans-dark-style .av-countdown-cell-inner .av-countdown-time-label{
	color: #000;
}

#top .av-trans-light-style .av-countdown-timer-title{
	color: #fff;
}

#top .av-trans-dark-style .av-countdown-timer-title{
	color: #000;
}

#av-admin-preview .av-countdown-timer{
	opacity: 1;
}


@media only screen and (max-width: 989px)
{
	.responsive .av-countdown-cell-inner{
		padding: 10px 10px;
	}
}

@media only screen and (max-width: 767px)
{
	.responsive .av-countdown-cell-inner{
		padding: 12px 7px;
	}

	.responsive .av-countdown-time{
		font-size: 30px;
	}

	.av-countdown-time-label{
		text-transform: none;
	}
}

/* Retro Flip Numbers stylings  */
.av-flip-numbers .av-countdown-timer-inner{
	display: flex;
	flex-direction: row;
	justify-content: center;
	flex-wrap:wrap;
	text-align: center;
	perspective: 400px;
	margin: 20px auto;
}

.av-flip-numbers .av-countdown-timer-inner *,
.av-flip-numbers .av-countdown-timer-inner *:before,
.av-flip-numbers .av-countdown-timer-inner *:after{
	box-sizing: border-box;
}

.av-flip-numbers .flip-numbers__piece{
	display: flex;
	flex-direction: column;
	align-content: center;
	justify-content: center;
	margin: 0 5px;
}

.av-flip-numbers .flip-numbers__piece > span{
	align-self: center;
}

.av-flip-numbers .card{
	display: block;
	position: relative;
	padding-bottom: 0.72em;
	font-size: 40px;
	font-weight: 700;
	line-height: 0.95;
}

.av-flip-numbers .card__top,
.av-flip-numbers .card__bottom,
.av-flip-numbers .card__back::before,
.av-flip-numbers .card__back::after{
	display: block;
	height: 0.72em;
	padding: 0.25em 0.25em;
	border-radius: 0.15em 0.15em 0 0;
	backface-visibility: hidden;
	transform-style: preserve-3d;
	width: 1.8em;
	transform: translateZ(0);
}

.av-flip-numbers .card__bottom{
	position: absolute;
	top: 50%;
	left: 0;
	border-top: solid 1px #000;
	border-radius: 0 0 0.15em 0.15em;
	pointer-events: none;
	overflow: hidden;
}

.av-flip-numbers .card__top,
.av-flip-numbers .card__bottom,
.av-flip-numbers .card__back::before,
.av-flip-numbers .card__back::after{
	background-color: #2c2c2c;
	color: #f8f8f8;
}

.av-flip-numbers.av-default-dark .card__top,
.av-flip-numbers.av-default-dark .card__back::before,
.av-flip-numbers.av-default-dark .card__back::after{
	color: #ccc;
	background-color: #2c2c2c;
	background: linear-gradient( to bottom, #2c2c2c, #434343);
}

.av-flip-numbers.av-default-light .card__top,
.av-flip-numbers.av-default-light .card__back::before,
.av-flip-numbers.av-default-light .card__back::after{
	color: #2c2c2c;
	background-color: #ededed;
	background: linear-gradient( to bottom, #ededed, #f8f8f8);
}

.av-flip-numbers.av-default-dark .card__bottom{
	border-top: solid 1px #000;
	background-color: #434343;
	color: #f8f8f8;
}

.av-flip-numbers.av-default-light .card__bottom{
	border-top: solid 1px rgba(0,0,0,0.2);
	background-color: #f8f8f8;
	color: #616161;
}

.av-flip-numbers .card__bottom::after{
	display: block;
	margin-top: -0.72em;
}

.av-flip-numbers .card__back::before,
.av-flip-numbers .card__bottom::after{
	content: attr(data-value);
}

.av-flip-numbers .card__back{
	position: absolute;
	top: 0;
	height: 100%;
	left: 0%;
	pointer-events: none;
}

.av-flip-numbers .card__back::before{
	position: relative;
	z-index: -1;
	overflow: hidden;
}

.av-flip-numbers .flip .card__back::before{
	animation: av-flip-numbers-flipTop 0.3s cubic-bezier(0.37, 0.01, 0.94, 0.35);
	animation-fill-mode: both;
	transform-origin: center bottom;
}

.av-flip-numbers .flip .card__back .card__bottom{
	transform-origin: center top;
	animation-fill-mode: both;
	animation: av-flip-numbers-flipBottom 0.6s cubic-bezier(0.15, 0.45, 0.28, 1);
}


.av-flip-clock{
	text-align: center;
}

.av-flip-clock .av-countdown-timer-inner{
	display:flex;
	flex-wrap:wrap;
	justify-content:center;
}

.av-flip-clock .flip-clock__piece{
	display: flex;
	flex-direction: column;
	align-content: center;
	justify-content: center;
	margin: 0 5px;
}

.av-flip-clock .flip-clock__piece > span{
	align-self: center;
}

.av-flip-clock .flip-clock__card{
	border-radius: 0.15em;
	box-shadow: 0 0 10px 0 rgba(0,0,0,.5);
	display: block;
	position: relative;
	line-height: 0.95;
	height: 1.5em;
	width: 1.5em;
	perspective: 479px;
	-webkit-backface-visibility: hidden;
	        backface-visibility: hidden;
}

.av-flip-clock .flip-clock-counter,
.av-flip-clock .flip-clock__card{
	text-align: center;
	transform: translateZ(0);
}

.av-flip-clock .flip-clock-counter{
	display:block;
	font-weight:700;
	line-height:1.5em;
	font-size:1em;
	overflow:hidden;
	position:absolute;
	top:0;
	width:100%;
	transform-style:preserve-3d;
	background-color: #2c2c2c;
	color: #f8f8f8;
}

.av-flip-clock .flip-clock-counter.top{
	border-top: 1px solid rgba(255,255,255,0.2);
	border-bottom: 1px solid rgba(255,255,255,0.1);
	border-radius: 0.15em 0.15em 0 0;
	height: 50%;
	transform-origin: 50% 100%;
}

.av-flip-clock.av-default-dark .flip-clock-counter.top{
	color: #ccc;
	background-color:#2c2c2c;
	background: linear-gradient( to bottom, #2c2c2c, #434343);
}

.av-flip-clock.av-default-light .flip-clock__card{
	box-shadow: 0 0 10px 0 rgba(255, 255, 255, 0.7);
}

.av-flip-clock.av-default-light .flip-clock-counter.top{
	border-top: 1px solid rgba(0,0,0,0.2);
	border-bottom: 1px solid rgba(0,0,0,0.1);
	color: #2c2c2c;
	background-color:#ededed;
	background: linear-gradient( to bottom, #ededed, #f8f8f8);
}

.av-flip-clock .flip-clock-counter.bottom {
	border-top: 1px solid #000;
	border-bottom: 1px solid #000;
	border-radius: 0 0 0.15em 0.15em;
	line-height: 0!important;
	height: 50%;
	top: 50%;
	transform-origin: 50% 0;
	/*background: linear-gradient( to top, #000000, #ffffff );;*/
}

.av-flip-clock.av-default-dark .flip-clock-counter.bottom {
	border-top: 1px solid #fff;
	border-bottom: 1px solid #fff;
	background-color: #434343;
	color: #f8f8f8;
}

.av-flip-clock.av-default-light .flip-clock-counter.bottom {
	border-top: 1px solid rgba(0,0,0,0.2);
	border-bottom: 1px solid rgba(0,0,0,0.1);
	background-color: #f8f8f8;
	color: #616161;
}


.av-flip-clock .flip-clock-counter.curr.top {
	transform: rotateX(0deg);
	z-index: 3;
}

.av-flip-clock .flip-clock-counter.next.bottom {
	transform: rotateX(90deg);
	z-index: 2;
}

.av-flip-clock .flip .flip-clock-counter.curr.top {
	transition: all .25s ease-in-out;
	transform: rotateX(-90deg);
}

.av-flip-clock:not(.av-flip-bounce) .flip .flip-clock-counter.next.bottom {
	transition: all .25s ease-in-out .25s;
	transform: rotateX(0deg);
}

.av-flip-clock.av-flip-bounce .flip .flip-clock-counter.next.bottom {
	animation: av-flip-clock-flipBottom 0.6s cubic-bezier(0.37, 0.01, 0.94, 0.35);
	animation-fill-mode: both;
	transform-origin: center top;
}

.av-flip-clock .flip-clock_label {
	font-size: inherit;
	padding: 5px 2px;
	display: block;
}

@media only screen and (max-width:767px)
{
	.av-flip-clock .flip-clock__piece {
		margin: 0 2px;
	}

	.av-flip-clock .flip-clock_label {
		font-size: 0.7em;
	}
}

@keyframes av-flip-numbers-flipTop {
  0% {
    transform: rotateX(0deg);
    z-index: 2;
  }
  0%,
  99% {
    opacity: 0.99;
  }
  100% {
    transform: rotateX(-90deg);
    opacity: 0;
  }
}

@keyframes av-flip-numbers-flipBottom {
  0%,
  50% {
    z-index: -1;
    transform: rotateX(90deg);
    opacity: 0;
  }
  51% {
    opacity: 0.99;
  }
  100% {
    opacity: 0.99;
    transform: rotateX(0deg);
    z-index: 5;
  }
}

@keyframes av-flip-clock-flipBottom {
  0% {
    z-index: -1;
    transform: rotateX(90deg);
    opacity: 0;
  }
  51% {
    opacity: 0.99;
    transform: rotateX(60deg);
    z-index: 2;
  }
  65% {
    opacity: 0.99;
    transform: rotateX(30deg);
    z-index: 2;
  }
  70% {
    opacity: 0.99;
    transform: rotateX(15deg);
    z-index: 2;
  }
  80% {
    opacity: 0.99;
    transform: rotateX(-30deg);
    z-index: 2;
  }
  90% {
    opacity: 0.99;
    transform: rotateX(30deg);
    z-index: 2;
  }
  100% {
    opacity: 0.99;
    transform: rotateX(0deg);
    z-index: 2;
  }
}

