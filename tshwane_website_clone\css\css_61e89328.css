/* ======================================================================================================================================================
AVIACCORDION
====================================================================================================================================================== */

#top .aviaccordion{
	position: relative;
	overflow: hidden;
	clear: both;
}

#top .avia-accordion-slider-wrap{
	border:none;
	z-index: 2;
}

#top .avia-accordion-slider-wrap.el_after_av_slideshow_accordion{
	box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.4);
}

#top .aviaccordion-inner,
#top .aviaccordion-slide,
#top .aviaccordion-slide-link{
	position: absolute;
	margin:0;
	padding:0;
	width:100%;
	height:100%;
	list-style-type: none;
}

#top .aviaccordion-slide img{
	height:100%;
	position: absolute;
	display:block;
	opacity: 0;
}

#top .aviaccordion-slide{
	box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.4);
	z-index: 2;
	overflow: hidden;
}

#top .aviaccordion-preview{
	position: absolute;
	z-index: 10;
	height:100%;
}

#top .aviaccordion-preview-title-pos{
	position: absolute;
	bottom: 0;
	left:0;
	z-index: 10;
	width:100%;
	height:100%;
	opacity: 0;
}

#top .aviaccordion-preview-title-wrap{
	position: relative;
	display: table;
	height:100%;
	width:100%;
	background:#000;
	background: rgba(0,0,0,0.5);
	padding:0;
}

#top .aviaccordion-preview-title{
	display: table-cell;
	vertical-align: middle;
	color:#fff;
	-webkit-font-smoothing: subpixel-antialiased;
	padding:10%;
}

#top .aviaccordion-preview-title h3{
	color:#fff;
}

#top .aviaccordion-excerpt{
	line-height: 1.65em;
}

#top .aviaccordion .av-accordion-text-center{
	text-align: center;
}

#top .aviaccordion-title{
	text-transform: uppercase;
	font-size: 14px;
}

#top .aviaccordion-title-on-hover .aviaccordion-preview{
	opacity: 0;
	text-align: center;
}

#top .aviaccordion-title-on-hover .aviaccordion-active-slide .aviaccordion-preview{
	opacity: 1;
}

#top .aviaccordion.av-animation-active .aviaccordion-slide,
#top .aviaccordion-title-on-hover .aviaccordion-preview{
	transition: all 0.7s cubic-bezier(0.230, 1.000, 0.320, 1.000);
}

@media only screen and (max-width: 767px)
{
	.responsive #top .aviaccordion-title-no-mobile .aviaccordion-preview-title-pos{
		display:none;
	}
}
