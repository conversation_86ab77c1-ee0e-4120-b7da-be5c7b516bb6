/* Table of Content
======================================================================================================================================================
	#Site Styles
	#Blog Styles
	#Page Styles
	#Post Formats
	#Widget & Sidebar
	#Footer & #Socket
	#Comment
	#CSS Animations
	#Media Queries
	*/

/* ======================================================================================================================================================
#Site Styles
====================================================================================================================================================== */

#header{
	position: relative;
	z-index: 501; /*(issues/769)*/
	width:100%;
	background: transparent;
}

#header_main .container,
.main_menu ul:first-child > li a{
	height: 88px;
	line-height: 88px;
}

/*	header reading progress bar	*/
#header .header-reading-progress{
	position: absolute;
	height: 2px;
	width: 0%;
	border-radius: 0 5px 5px 0;
	/*filter: drop-shadow( 0 3px 5px rgba(0,0,0,0.4) );*/
	transition: all 0.1s linear;
}

#header .header-reading-progress.medium-bar{
	height: 4px;
}

#header .header-reading-progress.large-bar{
	height: 6px;
}

#header .header-reading-progress.very-large-bar{
	height: 8px;
}

/* sticky variation*/
.html_header_top.html_header_sticky #main{
	padding-top:88px;
}

.html_header_top.html_header_transparency #header{
	position: absolute;
}

.html_header_top.html_header_sticky #header{
	position: fixed;
}

.html_header_top.html_header_sticky.html_bottom_nav_header #main{
	padding-top:140px;
}

.html_bottom_nav_header .avia-search-tooltip {
	top: 101% !important;
	margin-left: -130px;
}

.html_header_top.html_header_sticky.html_header_unstick_top .header-scrolled-full#header {  }


/*big header*/
.html_header_top.html_header_sticky.html_large #main{
	padding-top:116px;
}

.html_header_top.html_header_sticky.html_large #header_main .container,
.html_header_top.html_header_sticky.html_large.html_main_nav_header .main_menu ul:first-child > li a {
	height: 116px;
	line-height: 116px;
}

.html_header_top.html_header_sticky.html_large.html_bottom_nav_header #main{
	padding-top:168px;
}

/* stretched variation*/
.html_header_top #top .av_header_stretch .container{
	width:96%;
	padding:0;
	max-width: 100%;
}

/*logo + position variation*/
.html_header_top #top .av_logo_right .logo{
	left:auto;
	right:0;
}

.html_header_top #top .av_menu_left .main_menu{
	left:0px;
	right:auto;
}

.html_header_top.html_main_nav_header #top .av_menu_left .main_menu{
	left:-13px;
	right:auto;
}

.html_header_top .av_bottom_nav_header #header_main_alternate .main_menu ul:first-child > li > a {
	height:50px;
	line-height: 50px;
}

.html_header_top .av_bottom_nav_header .av-logo-container .main_menu{
	display:none;
}

.html_header_top.html_bottom_nav_header #header_main{
	z-index: 3;
}

.html_header_top.html_bottom_nav_header #header_main_alternate .main_menu{
	clear: both;
	position: relative;
	line-height: 40px;
	height: 100%;
	width:100%;
	float:left;
}

.html_header_top.html_bottom_nav_header #header_main_alternate .main_menu>div,
.html_header_top.html_bottom_nav_header #header_main_alternate .main_menu ul:first-child{
	width:100%;
	height:50px;
}

.html_header_top.html_bottom_nav_header #header_main_alternate .main_menu ul:first-child>li:hover{
	z-index: 1000;
	position: relative;
}

.html_header_top.html_bottom_nav_header #header_main_alternate .main_menu .menu-item-search-dropdown{
	float:right;
}

#header_main_alternate{
	z-index: 2;
}

#header #header_main_alternate .container{
	max-height: none;
	height:auto;
}

#header_main_alternate .main_menu .menu li ul ul {
	left: 207px;
}

#header_main_alternate .avia_mega_div{
	right:auto;
	left:0;
}

.html_header_top.html_logo_center .main_menu{
	text-align: center;
}

.html_header_top.html_logo_center #header_main_alternate .main_menu ul:first-child{
	display: inline-block;
	width:auto;
	position: static;
}

.html_header_top.html_logo_center .logo{
	left:50%;
	transform: translate(-50%, 0);
}

.avia-msie-8 .logo img,
.avia-msie-8 .logo svg{
	height: 100%;
}

.avia-msie-8.html_header_top.html_logo_center .logo {
	left:46%; /*ie8 rule for somewhat centering the logo*/
}

.html_header_top.html_bottom_nav_header.html_logo_right .main_menu ul:first-child{
	width:auto;
	float:right;
}

.html_header_top.html_bottom_nav_header.html_logo_right .main_menu ul:first-child{
	width:auto;
	float:right;}

/*top bar variation*/
.html_header_top.html_header_topbar_active.html_header_sticky #top #main{
	padding-top:119px;
}

.html_header_top.html_header_topbar_active.html_header_sticky.html_large #top #main{
	padding-top:147px;
}

.html_header_top.html_header_topbar_active.html_header_sticky.html_bottom_nav_header #top #main{
	padding-top:170px;
}

.html_header_top.html_header_topbar_active.html_header_sticky.html_large.html_bottom_nav_header #top #main{
	padding-top:198px;
}

/*top bar element alignment*/
.av_icon_active_left .social_bookmarks{
	float: left;
}

.av_icon_active_right .social_bookmarks{
	float: right;
}

.av_secondary_right .sub_menu{
	float:right;
}

.av_phone_active_left .phone-info{
	float: left;
}

.av_phone_active_right .phone-info{
	float: right;
}


/*header with social icons and bottom nav */
.phone-info {
	float: left;
	font-weight: bold;
	line-height: 20px;
	font-size: 11px;
	padding:5px 0;
}

.phone-info div{
	display: inline-block;
	line-height: 1em;
}

.av_secondary_left .sub_menu>ul>li:last-child,
.av_secondary_left .sub_menu>div>ul>li:last-child{
	border:none;
	padding-right: 0;
}

.av_secondary_right .sub_menu>ul>li:last-child,
.av_secondary_right .sub_menu>div>ul>li:last-child{
	border:none;
	margin-right: 0px;
	padding-right:0;
	padding-left:10px;
}

.av_secondary_left .sub_menu>ul>li:first-child{
	padding-left:0;
}

.av_icon_active_left.av_secondary_left .sub_menu>ul>li:first-child{
	padding-left:10px;
}

.av_icon_active_right.av_secondary_right .sub_menu>ul>li:first-child{
	padding-left:10px;
}

.av_secondary_right .sub_menu{
	padding-left:10px;
}

.av_icon_active_right .social_bookmarks{
	padding-left:20px;
}

.av_secondary_left .sub_menu{
	padding-right:10px;
}

/* .html_boxed.html_header_sticky #header{ width:auto; } */
.html_boxed #main {
	position: static; /*necessary for boxed layout + fix bg sections */
	overflow: hidden;
}

.html_logo_right.html_bottom_nav_header #header_main .social_bookmarks{
	right:auto;
	left:0;
}


/*header with social icons and main nav */
#top nav .social_bookmarks{
	position: relative;
	top: 50%;
	margin-top: -16px;
	right: 0;
	overflow: hidden;
	clear: none;
	float: left;
}

.avia-menu.av_menu_icon_beside{
	padding-right:25px;
	margin-right:25px;
	border-right-width: 1px;
	border-right-style: solid;
	transition: border-color 0.2s ease-in-out;
}

.fallback_menu + .social_bookmarks {
	padding-left: 18px;
}

#header_meta{
	border-top:none;
	z-index: 10;
	min-height: 30px;
	border-bottom-style: solid;
	border-bottom-width: 1px;
	margin-bottom:-1px;
}

#header_meta .container{
	min-height: 30px;
}

#header_main{
	border-bottom-width: 1px;
	border-bottom-style: solid;
	z-index: 1;
}

#header.shadow{
	box-shadow:0px 0px 3px rgba(0, 0, 0, 0.2);
}

.header_bg{
	position: absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;
	opacity:0.95;
	z-index: 0;
	transition: all 0.4s ease-in-out;
	-webkit-perspective: 1000px;
	-webkit-backface-visibility: hidden;
}

.html_header_sticky_disabled .header_bg {
	opacity: 1;
}

.avia-msie-8.html_header_transparency .av_header_sticky_disabled#header{
	background: transparent;
}

/*minimal header*/
.av_minimal_header .header_bg{
	opacity:1;
}

.av_minimal_header #header_main, .av_minimal_header #header_meta{
	border:none;
}

.av_minimal_header .avia-menu-fx{
	display: none;
}

#top #header.av_minimal_header .main_menu ul:first-child >li > ul,
#top #header.av_minimal_header .main_menu .avia_mega_div > .sub-menu{
	border-top: none;
}

.av_minimal_header #header_main .container,
.av_minimal_header .main_menu ul:first-child > li a{
	height:90px;
	line-height: 90px;
}

.html_header_top.html_header_sticky.html_large .av_minimal_header #header_main .container,
.html_header_top.html_header_sticky.html_large.html_main_nav_header .av_minimal_header .main_menu ul:first-child > li a{
	height:118px;
	line-height: 118px;
}

#top .av_minimal_header #s{
	border:none;
	padding: 12px 47px 12px 5px;
}

#top .av_minimal_header .av_ajax_search_entry{
	border:none;
}

.av_minimal_header_shadow{
	box-shadow: 0 0 2px 1px rgba(0,0,0,0.1);
}

.av_minimal_header_shadow.av_header_transparency{
	box-shadow: 0 0 0px 0px rgba(0,0,0,0.0);
}

.av_minimal_header #header_main_alternate{
	border:none;
}


/*transparent header*/
.html_header_transparency #main{
	padding-top: 0 !important;
}

#top .av_header_transparency .main_menu ul:first-child > li > a:hover,
#top .av_header_transparency .main_menu ul:first-child > li > a:focus,
#top #wrap_all .av_header_transparency .sub_menu > ul > li > a:hover,
#top #wrap_all .av_header_transparency .sub_menu > ul > li > a:focus{
	opacity: 0.8;
	transition: opacity 0.4s ease-in-out;
}

#top .av_header_transparency.av_alternate_logo_active .logo a > img,
#top .av_header_transparency.av_alternate_logo_active .logo a > svg{
	opacity: 0;
}

#top .av_header_transparency #header_main,
#top .av_header_transparency .avia-menu.av_menu_icon_beside{
	border-color: transparent;
}

#top .av_header_transparency .header_bg{
	background-color: transparent;
	opacity: 0;
}

#top .av_header_transparency .phone-info, #top .av_header_transparency .social_bookmarks li a {
	color:inherit;
}

#top #header.av_header_transparency .avia_mega_div{
	margin-top:1px;
	padding-top:1px;
}

#top .av_header_transparency .avia-menu-fx {
	bottom: 22%;
	width: 70%;
	left: 15%;
}

#top .av_header_transparency .phone-info.with_nav span{
	border-color: rgba(255, 255, 255, 0.25);
	color:#fff;
}

#top .av_header_transparency #header_meta li{
	border-color: rgba(255,255,255,0.25);
	color:inherit;
}

#top .av_header_transparency #header_meta{
	background: transparent;
	box-shadow: none;
	border-bottom: 1px solid rgba(255,255,255,0.25);
}

.html_header_transparency #header_meta{
	transition: background 0.4s ease-in-out;
}


/*transparent glassy*/
#top .av_header_glassy.av_header_transparency .header_bg{
	background-color: #fff;
	opacity: 0.1;
}

#top .av_header_glassy.av_header_transparency #header_main{
	border-color: rgba(255,255,255,0.25);
	border-top-color: transparent;
}

#top .av_header_glassy.av_header_transparency .avia-menu.av_menu_icon_beside{
	border-color: rgba(255, 255, 255, 0.25);
}

#top .av_header_glassy.av_header_transparency .social_bookmarks li,
#top .av_header_glassy.av_header_transparency .social_bookmarks li a{
	border-color: rgba(255,255,255,0.25);
	color:#fff;
}

#top .av_header_glassy.av_header_transparency #header_main_alternate{
	border-bottom-color: rgba(255,255,255,0.25);
}

/*with border*/
#top .av_header_with_border.av_header_transparency #header_main{
	border: 1px solid rgba(255,255,255,0.25);
	border-left: none;
	border-right: none;
	border-top-color: transparent;
}

#top .av_header_with_border.av_header_transparency .avia-menu.av_menu_icon_beside{
	border-color: rgba(255,255,255,0.25);
}


/*disabled search icon*/
.html_header_searchicon_disabled #top .av_header_border_disabled.av_bottom_nav_disabled .main_menu .menu>li:last-child>a .avia-menu-fx{
	padding-right:13px;
	box-sizing: content-box;
}


/*scrolldown header*/
#top .av_header_scrolldown{
	transition: opacity 0.6s ease-out, margin 0.6s ease-out;
	margin-top:0px;
}

#top .av_header_scrolldown.av_header_transparency{
	opacity: 0;
	margin-top:-250px !important;
}

.html_header_transparency.html_header_scrolldown #top .avia-builder-el-0 .container,
.html_header_transparency.html_header_scrolldown #top .avia-builder-el-0 .slideshow_inner_caption {
	padding-top: 0;
}

/* page as footer fix */
.html_header_transparency #top .footer-page-content .avia-builder-el-0 .container{
	padding-top: 0;
}

/*perma hidden*/
#top .header_hidden{
	display:none;
}


/*header separator variations*/
.av_seperator_small_border .av-main-nav > li > a > .avia-menu-text{
	border-left-style: solid;
	border-left-width:1px;
	padding-left: 13px;
	margin-left: -13px;
}

.av_seperator_small_border .av-main-nav > li:first-child > a > .avia-menu-text{
	border-left:none;
}

.av_seperator_big_border .av-main-nav > li > a{
	border-left-style: solid;
	border-left-width:1px;
	text-align: center;
	min-width: 90px;
}

#top .av_seperator_big_border .av-main-nav > li:last-child > a,
#top .av_seperator_big_border .av-main-nav > #menu-item-search > a{
	border-right-style: solid;
	border-right-width:1px;
}

#top .av_seperator_big_border .av-main-nav > #menu-item-search > a{
	border-left-style: solid;
	border-left-width:1px;
	border-color: inherit;
}

#top .av_seperator_big_border#header .av-menu-button > a .avia-menu-text{
	border:none;
}

.av_seperator_big_border .avia-menu.av_menu_icon_beside{
	padding-right: 0;
}

#top .av_seperator_big_border#header .av-main-nav > li > a{
	padding:0 13px;
}

#top .av_seperator_big_border .avia-menu.av_menu_icon_beside{
	border-right-style:none;
	border-right-width:0;
}

.html_bottom_nav_header #top .av_seperator_big_border .av-main-nav > #menu-item-search > a{
	border-left-style: solid;
	border-left-width:1px;
	border-color: inherit;
	margin-left: -1px;
}


.avia-menu-subtext,
#top .sub-menu .avia-menu-subtext{
	display:none;
}

/*menu flyout position*/
.html_header_top #top .av_menu_left .main_menu .menu li ul ul{
	left: 207px;
}


/*sidebar headers*/
.html_header_sidebar{}

.html_header_sidebar #top #header{
	width:300px;
	position: absolute;
	min-height:100%;
	border-bottom:none;
}

.html_boxed.html_header_sidebar #wrap_all{
	position: relative;
}

.html_header_sidebar #top #header_main{
	border-top:none;
}

.html_header_sidebar #header .container{
	width:100%;
	height:auto;
	line-height: 1.3em;
	padding:0;
	float:none;
	max-width: 100%;
}

.html_header_sidebar .header_bg{
	opacity: 1;
}

.html_header_left #top  #header{}

.html_header_right #top #header{
	right: 0;
}

.html_header_left #main{
	margin-left:300px;
	position: relative;
	border-left-style:solid;
	border-left-width:1px;
}

.html_header_right #main{
	margin-right:300px;
	position: relative;
	border-right-style:solid;
	border-right-width:1px;
}

.html_header_sidebar #top #header.av_always_sticky{
	position: fixed;
	-webkit-backface-visibility: hidden; /*fix for layout bugs when scrolling*/
}

.html_header_sidebar .logo{
	position: relative;
	clear:both;
	padding:40px;
	border-bottom-style: solid;
	border-bottom-width:1px;
	height:auto;
	max-height: none;
	width:100%;
}

.html_header_sidebar .logo img,
.html_header_sidebar .logo svg{
	width: 100%;
	max-height: none;
	padding: 0;
	height: auto;
}

.html_header_sidebar .main_menu{
	position: relative;
	clear:both;
	z-index: 200;
}

.html_header_sidebar #header .av-main-nav , .html_header_sidebar #header .av-main-nav-wrap{
	width:100%;
}

.html_header_sidebar #header .av-main-nav {
	padding:20px 0;
}

.html_header_sidebar #header .av-main-nav > li{
	float:none;
	display: block;
	margin:0 13%;
}

.html_header_sidebar #header .av-main-nav > li > a{
	line-height: 1.3em;
	height:auto;
	padding:15px 3px;
	border-bottom-style: solid;
	border-bottom-width:1px;
	margin:0 auto;
}

.html_header_sidebar #header .av-main-nav > li > a .avia-menu-text{
	font-size: inherit;			/* 16px removed in 4.9.1 - overrides advanced styling setting */
	font-weight: normal;
	display: block;
}

.html_header_sidebar #header .av-main-nav > li > a .avia-menu-subtext{
	display:block;
	font-weight:normal;
	font-size:12px;
	padding-top: 3px;
}

.html_header_sidebar #header .av-main-nav > li:last-child > a{
	border:none;
}

.html_header_sidebar #header .av-main-nav > li:nth-last-child(2) > a{
	border:none;
}

.html_header_sidebar #header .avia-menu-fx{
	display:none;
}

.html_header_sidebar .av-main-nav ul{
	border-top-width: 1px;
}

.html_header_left .av-main-nav ul{
	top:0;
	left:100%;
	box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.1);
}

.html_header_left #top .av-main-nav ul ul{
	left:207px;
	top:-1px;
}

.html_header_right .av-main-nav ul{
	top:0;
	left:auto;
	right:100%;
	box-shadow: -5px 5px 15px rgba(0, 0, 0, 0.1);
}

.html_header_right #top .av-main-nav ul ul{
	left:-207px;
	top:-1px;
}

.html_header_sidebar #top .av-main-nav ul a{
	padding: 12px 15px;
}

.html_header_sidebar #main > .avia-builder-el-0{
	border-top:none;
}


.html_header_sidebar #top #header .av-menu-button > a{
	border:none;
}

.html_header_sidebar #top #header .av-menu-button + .av-menu-button > a{
	padding-top:0;
}

.html_header_sidebar #top #header .av-menu-button-colored:hover{
	opacity: 0.8;
}


.html_header_left  #header .avia_mega_div{
	left:100%;
	top:0;
}

.html_header_right #header .avia_mega_div{
	right:100%;
	top:0;
}

.html_header_sidebar #top #header .avia_mega_div > .sub-menu{
	border-width: 1px;
	border-style:solid;
}


.html_header_sidebar.html_content_align_left  .container {
	float:left;
}

.html_header_sidebar.html_content_align_right .container {
	float:right;
}


.html_header_sidebar.html_content_align_left #footer,
.html_header_sidebar.html_content_align_right #footer,
.html_header_sidebar.html_content_align_left #socket,
.html_header_sidebar.html_content_align_right #socket
{
	overflow: hidden;
}

.html_header_sidebar .container_wrap {
	width: 100%;
}


#header .avia-custom-sidebar-widget-area{
	margin:0;
	padding:0;
	float:none;
}

#header .avia-custom-sidebar-widget-area .widget{
	border-top-style: solid;
	border-top-width:1px;
	padding:40px;
	width:100%;
	overflow: hidden;
}


.html_header_sidebar .av-sidebar-social-container{
	position: relative;
	clear: both;
	overflow: hidden;
}

.html_header_sidebar #top #header .social_bookmarks{
	position: relative;
	top:0;
	margin:0;
	border-top-style: solid;
	border-top-width:1px;
	clear:both;
	display:table;
	width:100%;
	table-layout: fixed;
}

.html_header_sidebar #top #header .social_bookmarks li{
	float:none;
	display:table-cell;
	text-align: center;
}

.html_header_sidebar #top #header .social_bookmarks li.social_icon_1 a{
	border:none;
}

.html_header_sidebar #top #header .social_bookmarks li a{
	width:100%;
	border-radius: 0;
	border-left-style: solid;
	border-left-width: 1px;
	padding:10px 0;
}

.html_header_sidebar .av_default_container_wrap, .html_header_sidebar .container_wrap_first{
	float: left;
}

/*main header on top, logo below*/
.html_top_nav_header #header_main_alternate{
	border-top:none;
	border-bottom-style: solid;
	border-bottom-width:1px;
}

.html_top_nav_header.html_minimal_header #header_main_alternate{
	border:none;
}
.html_top_nav_header div .logo{
	position: relative;
}

.html_top_nav_header .av-logo-container .inner-container{
	position: relative;
	overflow: hidden;
}

.html_top_nav_header.html_header_top.html_header_sticky #top #wrap_all #main{
	padding-top:50px;
}

.html_top_nav_header.html_header_top.html_header_sticky.html_header_topbar_active #top #wrap_all #main{
	padding-top:80px;
}

.html_top_nav_header .av-logo-container{
	height:88px;
}

.html_top_nav_header.html_large .av-logo-container{
	height:150px;
}

.html_top_nav_header #header_main{
	border:none;
}


/*burger menu*/
.av-hamburger {
	padding: 0 0 0 0;
	display: inline-block;
	cursor: pointer;
	font: inherit;
	color: inherit;
	text-transform: none;
	background-color: transparent;
	border: 0;
	margin: 0;
	overflow: visible;
}


.av-hamburger-box {
	width: 35px;
	height: 24px;
	display: inline-block;
	position: relative;
}

.av-hamburger-inner {
	display: block;
	top: 50%;
	margin-top: -2px;
}

.av-hamburger-inner,
.av-hamburger-inner::before,
.av-hamburger-inner::after {
	width: 40px;
	height: 3px;
	background-color: #000;
	border-radius: 3px;
	position: absolute;
	transition: transform 0.15s ease;
}

.av-hamburger-inner::before,
.av-hamburger-inner::after {
	content: "";
	display: block;
}

.av-hamburger-inner::before {
	top: -10px;
}

.av-hamburger-inner::after {
	bottom: -10px;
}

/*
 * Spin
 */
.av-hamburger--spin .av-hamburger-inner {
	transition-duration: 0.3s;
	transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.av-hamburger--spin .av-hamburger-inner::before {
	transition: top 0.1s 0.34s ease-in, opacity 0.1s ease-in, background-color 0.15s ease;
}

.av-hamburger--spin .av-hamburger-inner::after {
	transition: bottom 0.1s 0.34s ease-in, transform 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) , background-color 0.15s ease;
}

.av-hamburger--spin.is-active .av-hamburger-inner {
	transform: rotate(225deg);
	transition-delay: 0.14s;
	transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

.av-hamburger--spin.is-active .av-hamburger-inner::before {
	top: 0;
	opacity: 0;
	transition: top 0.1s ease-out, opacity 0.1s 0.14s ease-out, background-color 0.15s ease;
}

.av-hamburger--spin.is-active .av-hamburger-inner::after {
	bottom: 0;
	transform: rotate(-90deg);
	transition: bottom 0.1s ease-out, transform 0.3s 0.14s cubic-bezier(0.215, 0.61, 0.355, 1), background-color 0.15s ease;
}


/*own additions*/
.av-burger-menu-main{
	display:none;
	transition: padding 0.3s ease-out;
}

.js_active.html_burger_menu #avia-menu > li{
	display:none;
}

.js_active.html_burger_menu #avia-menu .av-burger-menu-main,
.html_burger_menu #top #avia-menu .menu-item-search-dropdown{
	display:block;
}

.av-burger-menu-main{
	cursor: pointer;
}

.av-burger-menu-main a{
	padding-left:10px;
}

.av-hamburger strong{
	display:none;
}

.av-hamburger-box {
    height: 8px;
}

.av-hamburger-inner,
.av-hamburger-inner::before,
.av-hamburger-inner::after {
	width:100%;
}

.html_burger_menu #top #wrap_all .menu-item-search-dropdown > a{
	font-size:24px;
}

html.av-burger-overlay-active #top .menu-item-search-dropdown > a{
	color:#fff;
}

.html_burger_menu_active #header .avia-menu .menu-item{
	display: none;
}

.html_burger_menu_active .menu-item-avia-special{
	display:block;
}

.html_burger_menu_active #top #wrap_all .menu-item-search-dropdown > a {
	font-size: 24px;
}

.html_header_sidebar #top div .av-burger-menu-main{
	display:none;
}

.html_burger_menu_active #top #wrap_all #header .av-burger-menu-main > a{
	background: transparent;
	position: relative;
	z-index: 10;
}

/*
* Spin Reverse
*/
.av-hamburger--spin-r .av-hamburger-inner {
	transition-duration: 0.3s;
	transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.av-hamburger--spin-r .av-hamburger-inner::before {
	transition: top 0.1s 0.34s ease-in, opacity 0.1s ease-in;
}

.av-hamburger--spin-r .av-hamburger-inner::after {
	transition: bottom 0.1s 0.34s ease-in, transform 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.av-hamburger--spin-r.is-active .av-hamburger-inner {
	transform: rotate(-225deg);
	transition-delay: 0.14s;
	transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

.av-hamburger--spin-r.is-active .av-hamburger-inner::before {
	top: 0;
	opacity: 0;
	transition: top 0.1s ease-out, opacity 0.1s 0.14s ease-out;
}

.av-hamburger--spin-r.is-active .av-hamburger-inner::after {
	bottom: 0;
	transform: rotate(-90deg);
	transition: bottom 0.1s ease-out, transform 0.3s 0.14s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.av-burger-overlay{
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0;
	z-index:100;
	display: none;
	overflow: hidden;
	opacity: 0;
}

.av-burger-overlay-inner,
.av-burger-overlay-bg{
	position: absolute;
	top: 0;
	left: 0;
	height:100%;
	width: 100%;
	min-height: 700px;
	display:table;
	vertical-align: middle;
	text-align: center;
	z-index: 5;
}

.av-burger-overlay-bg{
	z-index: 3;
	opacity: 0.9;
	background: #000;
	display: block;
	position: fixed;
}

.av-burger-overlay-scroll{
	overflow: auto;
	position: absolute;
	height: 100%;
	width: 100%;
	z-index: 10;
	-webkit-overflow-scrolling: touch;
}


#av-burger-menu-ul li a{
	position: relative;
	display: block;
	transition: none;
}

#av-burger-menu-ul li ul {
	background:transparent;
}

html.av-burger-overlay-active #header_meta,
html.av-burger-overlay-active #menu-item-shop.cart_dropdown,
html.av-burger-overlay-active .cart_dropdown,
html.av-burger-overlay-active #top .social_bookmarks{
    z-index: 1;
}

.av-burger-overlay-active #top .av-hamburger-inner,
.av-burger-overlay-active #top .av-hamburger-inner::before,
.av-burger-overlay-active #top .av-hamburger-inner::after{
	background-color:#fff;
}

.av-burger-overlay-active #top #header .av-main-nav > li > a{
	background: transparent;
}

.av-burger-overlay-active #scroll-top-link{
	z-index:150;
}

#top #av-burger-menu-ul{
	display: table-cell;
	height: 100%;
	width:100%;
	vertical-align: middle;
	padding:125px 0;
}

@media only screen and (orientation: landscape) and (max-width: 989px)
{
	#top #av-burger-menu-ul{
		vertical-align: top;
	}
}

.html_av-overlay-full #av-burger-menu-ul li{
	display:block;
	height:auto;
	width:100%;
	padding:0.3em 0;
	font-size: 35px;
}

.html_av-overlay-full #top #wrap_all #av-burger-menu-ul li{
	line-height:1.6em;
}

.html_av-overlay-full #top #wrap_all #av-burger-menu-ul li li{
	font-size: 0.6em;
	line-height:1.4em;
}

.html_av-overlay-full #top #wrap_all #av-burger-menu-ul li li li{
	font-size: 0.8em;
}

.html_av-overlay-full #top #wrap_all #av-burger-menu-ul li a{
	color:#fff;
	height:auto;
	line-height:inherit;
	font-size: inherit;
}

.html_av-overlay-full #av-burger-menu-ul li a:hover,
.html_av-overlay-full #av-burger-menu-ul li a:active,
.html_av-overlay-full #av-burger-menu-ul li a:focus{
	text-decoration: none;
	opacity: 0.7;
}

.html_av-overlay-full.av-burger-overlay-active #top #wrap_all #menu-item-search a,
.html_av-overlay-full.av-burger-overlay-active #top #wrap_all #menu-item-search a:hover{
	color:#fff;
}


.html_header_top.html_logo_center.av-burger-overlay-active .menu-item-avia-special{
	z-index: 150;
}

#top #wrap_all #av-burger-menu-ul > li{
	opacity:0;
	position: relative;
	top:18px;
	transition: opacity 0.3s ease-out, top 0.3s ease-out, left 0.3s ease-out;
	transform: translate3d(0,0,0); /*fixes disapearing in ios*/
}

.avia_desktop.avia-safari #top #wrap_all #av-burger-menu-ul > li {
	transform: none;
}

#top #wrap_all #header #av-burger-menu-ul > li.av-active-burger-items{
	opacity:1;
	top:0;
	left:0;
}


.html_av-overlay-full #top #av-burger-menu-ul .av-menu-button > a .avia-menu-text{
	padding: 0.5em 1.5em;
}

.html_av-overlay-full #top #av-burger-menu-ul .av-menu-button{
	margin-top: 0.4em;
}

.html_av-overlay-full #av-burger-menu-ul li ul li a {
	font-size: 1.3em;
}


#av-burger-menu-ul li a .avia-menu-subtext{
	display: block;
	font-size: 0.6em;
	line-height: 1.2em;
	margin: -10px 0 13px 0;
	opacity: 0.7;
	text-transform: none;
}

.html_av-overlay-side  #av-burger-menu-ul li a .avia-menu-subtext{
	font-size:1em;
	margin:0;
	opacity: 0.9;
}

#av-burger-menu-ul .avia-menu-fx{
	display:none;
}

/*small*/
#top .av-small-burger-icon{
	transform: scale(0.6);
	transform-origin: right;
}

#top #wrap_all #header .av-small-burger-icon a{
	padding:0;
}


/*side opening menu*/
.html_av-overlay-side .av-burger-overlay-bg{
	opacity: 0.3;
	cursor: pointer;
}

.html_av-overlay-side #top .av-burger-overlay li {
	margin:0;
}

.html_av-overlay-side #top #wrap_all .av-burger-overlay li a{
	line-height: 1.3em;
	height:auto;
	padding:15px 50px;
	display: block;
	text-align: left;
	text-decoration: none;
}

.html_av-overlay-side #top .av-burger-overlay li a:hover{
	text-decoration: none;
}
.html_av-overlay-side #top #wrap_all #av-burger-menu-ul > li{
	top:0;
	left:18px;
}

.html_av-overlay-side #top #wrap_all .av-burger-overlay li li a{
	padding-left:70px;
}

.html_av-overlay-side #top #wrap_all .av-burger-overlay li li li a{
	padding-left:90px;
}

.html_av-overlay-side #top #wrap_all .av-burger-overlay li li li li a{
	padding-left:110px;
}

.html_av-overlay-side #top #wrap_all .av-burger-overlay li li li li li a{
	padding-left:130px;
}

.html_av-overlay-side .av-burger-overlay-scroll{
	width:350px;
	right:0;
	max-width:100%;
	transform: translateX(350px);
	transition: all 0.5s cubic-bezier(0.75, 0, 0.25, 1);
}

.html_av-overlay-side.av-burger-overlay-active-delayed .av-burger-overlay-scroll{
	transform: translateX(0);
}

.html_av-overlay-side #top #wrap_all #av-burger-menu-ul > li{
	opacity:1;
	top:0;
	left:0;
}


/*side opening menu classic*/
.html_av-overlay-side-classic #top .av-burger-overlay{
	font-size: 1em;
}

.html_av-overlay-side-classic #av-burger-menu-ul{
	vertical-align: top;
}

.html_av-overlay-side-classic #top .av-burger-overlay li a{
	border-bottom-style: solid;
	border-bottom-width: 1px;
}

.html_av-overlay-side-classic #top .av-burger-overlay li li .avia-bullet{
	height: 1px;
	display: block;
	position: absolute;
	margin-top: 0;
	opacity: 0.3;
	border: none!important;
	width: 7px;
	left: 50px;
	top: 50%;
}

.html_av-overlay-side-classic #top .av-burger-overlay li li li .avia-bullet{
	left: 70px;
}

.html_av-overlay-side-classic #top .av-burger-overlay li li li li .avia-bullet{
	left: 90px;
}

.html_av-overlay-side-classic #top .av-burger-overlay li li li li li .avia-bullet{
	left: 110px;
}

.html_av-overlay-side-classic #top .av-burger-overlay li li li li li li .avia-bullet{
	left: 130px;
}


/*side opening menu minimal*/
.html_av-overlay-side-minimal .av-burger-overlay-bg{
	opacity: 0.1;
}

.html_av-overlay-side-minimal #top .av-burger-overlay{
	font-size: 1.1em;
}

.html_av-overlay-side-minimal #top .av-burger-overlay .sub-menu{
	font-size: 0.9em;
}


/*hidden submenu items*/
/*#top #av-burger-menu-ul .av-show-submenu > ul{display: block;}*/
.html_av-submenu-hidden #av-burger-menu-ul li ul{
	display:none;
}

.html_av-submenu-hidden .av-submenu-indicator{
	display: inline-block;
	padding:0 10px;
	font-size: 11px;
	opacity: 0.5;
	vertical-align: top;
	float: right;
	transition: all 0.2s ease-out;
	position: absolute;
	right: 30px;
	top: 50%;
	margin-top: -9px;
	height: 20px;
	line-height: 20px;
}

.html_av-submenu-hidden .av-submenu-indicator:before{
	content:"\E87d";
	font-family: 'entypo-fontello';
}

.html_av-submenu-hidden .av-show-submenu > a > .av-submenu-indicator{
	transform: rotate(90deg);
}


/*logo*/
div .logo{
	float: left;
	position: absolute;
	left: 0;
	z-index: 1;
}

.logo, .logo a{
	overflow: hidden;
	position: relative;
	display: block;
	height: 100%;
}

.logo img,
.logo svg{
	padding: 0;
	display: block;
	width: auto;
	height: auto;
	max-height: 100%;
	image-rendering: auto;
	position: relative;
	z-index: 2;
	height : 100%\9; /*hack: fixes ie8 logo*/
	height: auto\9; /*hack: fixes ie8 squished logo*/
	transition: opacity 0.4s ease-in-out;
}

.logo svg,
.logo.avia-img-svg-logo > a > img,
.logo .alternate.avia-img-svg-logo{
	height: 100%;
}

.logo img.alternate,
.logo .subtext.avia-svg-logo-sub svg{
	position: absolute;
	z-index: 1;
	top:0;
	left:0;
	opacity: 0;
}

.av_header_transparency .logo img.alternate,
.av_header_transparency .logo .subtext.avia-svg-logo-sub svg{
	opacity: 1;
}

/*menu*/
.main_menu{
	clear:none;
	position: absolute;
	z-index: 100;
	line-height:30px;
	height:100%;
	margin:0;
	right:0;
}

.main_menu .pointer_arrow_wrap{
	display:none;
}

.av-main-nav-wrap{
	float:left;
	position: relative;
	z-index: 3;
}

.av-main-nav-wrap ul{
	margin:0;
	padding: 0;
}


.av-main-nav{
	z-index: 110;
	position: relative;
}

.av-main-nav ul {
	display: none;
	margin-left:0;
	left:0;
	position: absolute;
	top: 100%;
	width: 208px;
	z-index: 2;
	padding:0;
	box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
	margin-top:-1px;
}

.av-main-nav ul li {
	margin:0;
	padding:0;
	width:100%;
}

.av-main-nav ul li a {
	border-right-style: solid;
	border-right-width:1px;
	border-left-style: solid;
	border-left-width:1px;
}

.av-main-nav ul li:last-child > a {
	border-bottom-style: solid;
	border-bottom-width: 1px;
}

.av-main-nav li{
	float:left;
	position:relative;
	z-index:20;
}

.av-main-nav li:hover{
	z-index: 100
}

.av-main-nav > li > ul {
	border-top-width:2px;
	border-top-style: solid;
}

.av-main-nav > li {
	line-height: 30px;
}

.av-main-nav li a {
	max-width:none; /* fixes IE8 menu width issue*/
}
.av-main-nav > li > a{
	display: block;
	text-decoration: none;
	padding:0 13px;
	font-weight: normal;
	font-weight: 600;
	font-size: 13px;
	transition: background-color 0.4s ease-in-out, color 0.4s ease-in-out, border-color 0.4s ease-in-out;
}

.av-main-nav > li > a,
div #menu-item-shop.cart_dropdown{
	transition: none;
	transition: background-color 0.4s ease-in-out, color 0.4s ease-in-out, border-color 0.4s ease-in-out;
}

.av_header_transparency .av-main-nav > li > a , .av_header_transparency #menu-item-shop.cart_dropdown{
	transition: border-color 0.2s ease-in-out;
}


#top .av-main-nav ul a{
	width:100%;
	height:auto;
	float:left;
	text-align:left;
	line-height:23px;
	padding:8px 15px;
	font-size: 12px;
	min-height: 23px;
	max-width: none;
	text-decoration: none;
	font-family: inherit;
}

#top .av-main-nav ul ul {
	left:-207px;
	top:0px;
	margin:0;
	border-top-style: solid;
	border-top-width: 1px;
	padding-top: 0px
}

.av-main-nav li:hover ul ul{
	display:none;
}

#top .av-main-nav li:hover > ul {
	display:block;
}


.avia-menu-fx{
	position: absolute;
	bottom:-1px;
	height:2px;
	z-index: 10;
	width:100%;
	left:0;
	opacity: 0;
	visibility: hidden;
}

.av-main-nav li:hover .avia-menu-fx,
.current-menu-item > a > .avia-menu-fx,
.av-main-nav li:hover .current_page_item > a > .avia-menu-fx{
	opacity: 1;
	visibility: visible;
}

.avia-menu-fx .avia-arrow-wrap{
	height:10px;
	width:10px;
	position: absolute;
	top:-10px;
	left:50%;
	margin-left:-5px;
	overflow: hidden;
	display:none;
	visibility: hidden;
}

.current-menu-item>a>.avia-menu-fx>.avia-arrow-wrap, .current_page_item>a>.avia-menu-fx>.avia-arrow-wrap{
	display:block;
}

.avia-menu-fx .avia-arrow-wrap .avia-arrow{
	top: 10px;
}


.html_main_nav_header.html_logo_left #top .main_menu .menu>li:last-child>a,
.html_bottom_nav_header #top #menu-item-search>a{
	padding-right:0;
}

.html_bottom_nav_header.html_logo_center #top .av_seperator_big_border #menu-item-search>a{
	padding-right:13px;
	border-right-style: solid;
	border-right-width: 1px;
}

.html_bottom_nav_header .av-logo-container .main_menu{
	display:none;
}


/*mega menu styles*/
.main_menu .avia-bullet{
	display:none
}

#top #header .menu-item-mega-parent.current-menu-item{
	overflow: visible!important;
}

#top #header .mega_menu_title a{
	color:inherit;
	font-size: 17px;
	line-height: 1.1em;
	padding:0;
	margin:0;
	background: transparent;
	border:none;
}

#top #header .mega_menu_title a:hover{
	text-decoration: underline;
}


#header .avia_mega_div{ /* use similar styles to .main_menu .menu ul */
	display: none;
	margin: -1px 0 0 0;
	right: 0;
	position: absolute;
	top: 100%;
	z-index: 2;
	box-shadow: 0 32px 60px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	padding:1px 0 0;
	text-align: left;
}

#header.av_minimal_header .avia_mega_div{
	margin:0;
	border-top-style:solid;
	border-top-width:1px;
}

#header.av_minimal_header .avia_mega_div .units{
	border-right:none;
}

#header .avia_mega_div .units{
	padding:0 14px 0 15px;
	margin:0;
	border-right-style:dotted;
	border-right-width:1px;
}

#header li:hover .avia_mega_div{
	display:block;
}

#top #header .avia_mega_div ul,
#top #header .avia_mega_div li{ /*reset list styles for mega menus*/
	position: relative;
	display:block;
	top:auto;
	left:auto;
	height:auto;
}

#top #header .avia_mega_div .sub-menu{
	overflow: hidden;
	width:100%;
	box-shadow: none;
	border-style:none;
	border-width:0px;
	position: relative;
	top:0;
	display:block;
	left:0;
	clear: both;
}

/*wrapper around all columns*/
#top #header .avia_mega_div > .sub-menu{
	display: table;
	padding:20px 30px 30px;
	border-top-style:solid;
	border-top-width:2px;
}

#top #header .avia_mega_div > .sub-menu.avia_mega_hr {
	padding-top: 30px;
}

/*columns*/
#top #header .avia_mega_div > .sub-menu > li{
	display: table-cell;
	float:none;
	padding-top:10px;
	padding-bottom:0;
	vertical-align: top;
}

#top #header .avia_mega_div > .sub-menu.avia_mega_hr{
	border-top-width:1px;
	border-top-style:dashed;
}

/*columns inner*/
#top #header .avia_mega_div > .sub-menu > li > ul{
	padding:0;
}


/*column lists*/
#top #header .avia_mega_div > .sub-menu > li > ul li{
	display: block;
	float: none;
	padding: 0;
	margin:0;
	list-style-type: circle;
	list-style-position: inside;
}

/*nested column lists*/
#top #header .avia_mega_div > .sub-menu > li > ul ul li{
	margin-left:15px;
}

/*column lists links*/
#top #header .avia_mega_div > .sub-menu > li > ul > li  a{
	width:auto;
	float:none;
	display: block;
	border:none;
	padding:3px 12px 3px 12px;
	font-weight: normal;
	height: auto;
	line-height: 23px;
}

#header .avia_mega_div .avia_mega_menu_columns_first{
	padding-left:0;
}

#header .avia_mega_div .avia_mega_menu_columns_last{
	padding-right:0;
	border-right-style:none;
	border-right-width:0;
}

.avia-bullet {
	display: block;
	position: absolute;
	height: 0;
	width: 0;
	top: 51%;
	margin-top: -3px;
	left: -3px;
	border-top: 3px solid transparent !important;
	border-bottom: 3px solid transparent !important;
	border-left: 3px solid green;
}

.avia_mega_div .avia-bullet {
	margin-top: 12px;
	left: 3px;
	display: block;
	top: 0;
}

/*mega text blocks*/
#header .mega_menu_title{
	margin-bottom:8px;
	font-size: 17px;
	line-height: 1.1em;
	font-weight: 600;
	display: block;
}

#header .avia_mega_text_block{
	line-height: 21px;
}

#top #header .avia_mega_div .sub-menu .avia_mega_text_block a{
	padding:0;
	display: inline;
	border:none;
	text-decoration: underline;
}


/*menu button style*/
#top #wrap_all #header .av-menu-button > a{
	background: transparent;
}

#top #wrap_all .av_header_border_disabled .av-menu-button > a{
	border:none;
}

.av-menu-button + .av-menu-button{
	margin-left: -10px;
}

.av-menu-button + .av-menu-button > a{
	padding-left:0px;
}

#top .av-menu-button > a .avia-menu-text{
	padding: 9px;
}

#top .av-menu-button > a .avia-menu-text{
	border: 2px solid;
	border-radius: 2px;
}

.av-menu-button > a .avia-menu-fx{
	display:none
}
.av-menu-button-colored  > a .avia-menu-text{
	padding: 11px 10px 10px 10px;
}

/* was introduced to have same height colored and ghost main menu buttons but does not work in certain situations: eg when border radius for all buttons is set to 100
#top .av-menu-button-colored  > a .avia-menu-text{border: 2px solid;  border-radius: 2px;position: relative;}
#top .av-menu-button-colored  > a .avia-menu-text:after{content: "";width: calc(100% + 4px);height: 1px;position: absolute;bottom:-2px;left: -2px;}
*/

.av-menu-button-colored  > a:hover .avia-menu-text{
	opacity: 0.9;
}

#top #header .av-menu-button > a .avia-menu-subtext{
	display:none;
}

#top #header .av-menu-button > a .avia-menu-text{
	text-align: center;
	white-space: nowrap;
}


/*ajax search*/
#top #menu-item-search{
	z-index: 100;
}

#top .menu-item-search-dropdown > a,
#searchform #searchsubmit,
.av_ajax_search_image, .iconfont{
	font-size: 17px;
}

#top #menu-item-search.menu-item-search-dropdown>a{
	border-left:none;
}

#top #menu-item-search:hover>a{
	background: transparent;
	color: inherit;
}

.avia-search-tooltip{
	position: absolute;
	z-index: 9999999;
	padding:0;
	width: 300px;
	top: 85% !important;
	margin-left: -120px;
	border-radius: 2px;
	box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.2);
	border-width:1px;
	border-style: solid;
}

.avia-search-tooltip .avia-arrow-wrap{
	width:20px;
	height:20px;
	position: absolute;
	top:-20px;
	right:10px;
	margin-left:-10px;
	overflow: hidden;
}

.avia-arrow{
	height:10px;
	width:10px;
	position: absolute;
	top:-6px;
	left:50%;
	margin-left:-5px;
	transform: rotate(45deg);
	border-width:1px;
	border-style: solid;
	visibility: hidden\9;
}

.avia-search-tooltip .avia-arrow{
	top: 15px;
}

#top #searchform {
	margin:0;
	padding:0;
}

#top #searchform>div{
	position: relative;
	max-width: 300px;
}

#top .widget #searchform>div{
	margin:0;
	max-width: 100%;
}

#top #searchform br{
	display:none;
}

#top #s{
	width:100%;
	position: relative;
	padding:11px 47px 11px 5px;
	z-index: 1;
	margin:0;
	box-shadow: none;
}

#top #searchsubmit, .ajax_load{
	width: 40px;
	height: 100%;
	line-height: 40px;
	padding: 0;
	position: absolute; right: 0; top : 0;
	z-index: 2;
	margin:0;
	border-radius: 0;
	min-width: 40px;
}

.avia_mobile #top #searchsubmit,
.avia_mobile .ajax_load{
	height: 41px;
}

.avia-search-tooltip #searchform>div{
	margin:16px;
}

.ajax_load{
	z-index: 5;
}

.ajax_load_inner{
	background: url("../images/layout/loading.gif") no-repeat scroll center center #fff;
	opacity: 0.5;
	position: absolute;
	top:0;
	left:0;
	right:0;
	bottom:0;
}

#top #searchform .ajax_search_response{
	line-height: 1.4em;
	font-size: 12px;
	margin:0;
}

.ajax_search_response h4{
	padding:20px 16px 2px 16px;
	font-size: 14px;
	margin:0;
}

.ajax_search_response h4:first-child{
	padding-top:0px;
	border:none;
}

#top div .av_ajax_search_entry{
	display:block;
	text-decoration: none;
	line-height: 1.4em;
	font-size: 12px;
	height:auto;
	padding:10px 16px;
	border-bottom-style: dashed;
	border-bottom-width: 1px;
	clear:both;
	overflow: hidden;
	position: relative;
}

#top div .av_ajax_search_entry:hover{
	background-color: rgba(0,0,0,0.04);
}

#top div .av_ajax_search_entry.with_image .av_ajax_search_image{
	background: transparent;
}

.av_ajax_search_content{
	overflow: hidden;
	display: block;
}

.av_ajax_search_title{
	display:block;
	font-weight: bold;
	text-transform: uppercase;
}

.ajax_not_found .av_ajax_search_title{
	text-transform: none;
}

.av_ajax_search_image{
	height:32px;
	line-height:32px;
	text-align: center;
	width:32px;
	float:left;
	margin-right:8px;
	border-radius: 40px;
	overflow: hidden;
	font-size: 15px;
}

.av_ajax_search_image img{
	display:block;
	border:none;
	max-width: 100%;
	min-height:32px;
	min-width:32px;
}

.ajax_search_excerpt {
	font-size: 12px;
	line-height: 1.4em;
	display: block;
	margin-top: 3px;
	font-style: italic;
}

#top div .av_ajax_search_entry_view_all{
	text-align: center;
	font-weight: bold;
	border:none;
}

#top div .ajax_not_found,
#top div .av_ajax_search_entry.ajax_not_found:hover{
	border:none;
	background: transparent;
}

/*title container*/

/*avia title big*/
.title_container{
	position: relative;
}

#top .title_container .container{
	padding-top:10px;
	padding-bottom:10px;
	min-height: 56px;
}

.title_container .main-title {
	margin: 0;
	font-size: 16px;
	position: relative;
	z-index: 2;
	min-height: 36px;
	line-height: 2.3em;
	top: 0;
	font-weight: 400;
}

.title_container .main-title a{
	text-decoration: none;
}

.title_meta, #top .portfolio-entry .title_meta{
	display: block;
	clear: both;
	position: relative;
	z-index: 1;
	margin-top:-1em;
	padding:0;
}

.title_meta p{
	margin: 1.3em 0 0 0;
}

.title_container .breadcrumb {
	z-index: 10;
	line-height: 15px;
	font-size: 11px;
	position: absolute;
	right: 50px;
	top:50%;
	margin-top: -7px;
}

.breadcrumb a{
	text-decoration: none;
}

.breadcrumb a:hover{
	text-decoration: underline;
}

.breadcrumb-trail .trail-before,
.breadcrumb-trail .trail-end,
.breadcrumb-trail .sep,
.breadcrumb-trail a,
.breadcrumb-trail .bbp-breadcrumb-current{
	display: block;
	float: left;
	padding:0px 3px;
}

.breadcrumb-trail span, .bbp-breadcrumb-current a{
	display: inline;
	padding:0;
	float:none;
}

.breadcrumb .sep{
	display: block;
	overflow: hidden;
	width:8px;
}


/*blank pages*/

#top.avia-blank #wrap_all #main{
	padding-top: 0 !important;
	margin:0;
}

#top.avia-blank #wrap_all #main .container {
	padding-top: 0 !important;
}

#top.avia-blank{
	height: 100%;
	position: absolute;
	width: 100% !important;
	margin: 0;
	display: table;
	vertical-align: middle;
	float: none;
	top:0;
	left:0;
	table-layout: fixed;
}

#top.boxed.avia-blank{
	max-width:100% !important;
}

#top.boxed.avia-blank .container{
	margin: 0 auto;
}

#top.avia-blank #wrap_all{
	display: table-cell;
	float: none;
	vertical-align: middle;
}

#top.avia-blank #wrap_all #main{
	padding:0;
}

#top.avia-blank #main .container_wrap:last-child{
	border-bottom-style: solid;
	border-bottom-width: 1px;
}

#top.avia-blank #main .container_wrap:first-child{
	border-top-style: solid;
	border-top-width: 1px;
}

/*--------------------framed layout---------------*/
.av-frame{
	position: fixed;
	z-index: 600;
}

.html_av-framed-box .av-frame.av-frame-vert{
	left:0;
	width:100%;
	top:0;
}

.html_av-framed-box .av-frame.av-frame-hor{
	top:0;
	height:100%;
	left:0;
}

.html_av-framed-box .av-frame.av-frame-bottom{
	top:auto;
	bottom:0;
}

.html_av-framed-box .av-frame.av-frame-right{
	left:auto;
	right:0;
}

.html_av-framed-box.html_av_admin_bar_active .av-frame.av-frame-top{
	margin-top:32px;
}

.html_header_top.html_header_sticky.html_av-framed-box #header{
	left:0;
}

.html_header_top.html_header_sticky.html_av-framed-box #header_main,
.html_header_top.html_header_sticky.html_av-framed-box #header_meta
{
	margin:0 50px;
}


/*--------------------alternate header styles---------------*/


/*social bookmarks*/

#top .social_bookmarks {
	height: 30px;
	z-index: 150;
	-webkit-backface-visibility: hidden;
	margin: 0 0 0 -9px;
}

#top .social_bookmarks li{
	height:100%;
	float:left;
	padding:0;
	transition: all 0.2s ease-in-out;
	border-right-style: solid;
	border-right-width: 1px;
	display: block;
	width:30px;
}

#top #header .social_bookmarks li:last-child{
	border-right-style:none;
	border-right-width:0;
}

#top .social_bookmarks li a{
	float:left;
	width:30px;
	line-height:30px;
	display: block;
	margin:0px;
	outline: none;
	padding:0;
	min-height:30px;
	height:100%;
	overflow: visible;
	z-index: 2;
	position: relative;
	text-align: center;
}

#top #wrap_all .social_bookmarks,
#top #wrap_all .social_bookmarks a,
#top #wrap_all .social_bookmarks li{
	background: transparent;
}

#top #wrap_all .social_bookmarks li a:hover{
	text-decoration: none;
}

#top #wrap_all .av-social-link-rss:hover a,
#top #wrap_all .av-social-link-rss a:focus{
	color: #fff;
	background-color: #ffa133;
}

#top #wrap_all .av-social-link-facebook:hover a,
#top #wrap_all .av-social-link-facebook a:focus{
	color: #fff;
	background-color: #37589b;
}

#top #wrap_all .av-social-link-twitter:hover a,
#top #wrap_all .av-social-link-twitter a:focus,
#top #wrap_all .av-social-link-square-x-twitter:hover a,
#top #wrap_all .av-social-link-square-x-twitter a:focus{
	color: #000;
	background-color: #fff;
}

#top #wrap_all .av-social-link-tiktok:hover a,
#top #wrap_all .av-social-link-tiktok a:focus{
	color: #00F2EA;
	background-color: #FF0050;
}

#top #wrap_all .av-social-link-whatsapp:hover a,
#top #wrap_all .av-social-link-whatsapp a:focus{
	color: #fff;
	background-color: #00e676;
}

#top #wrap_all .av-social-link-mail:hover a,
#top #wrap_all .av-social-link-mail a:focus{
	color: #fff;
	background-color: #9fae37;
}

#top #wrap_all .av-social-link-dribbble:hover a,
#top #wrap_all .av-social-link-dribbble a:focus{
	color: #fff;
	background-color: #e44885;
}

#top #wrap_all .av-social-link-linkedin:hover a,
#top #wrap_all .av-social-link-linkedin a:focus{
	color: #fff;
	background-color: #419cca;
}

#top #wrap_all .av-social-link-search:hover a,
#top #wrap_all .av-social-link-search a:focus{
	color: #fff;
	background-color: #222222;
}

#top #wrap_all .av-social-link-gplus:hover a,
#top #wrap_all .av-social-link-gplus a:focus{
	color: #fff;
	background-color: #de5a49;
}

#top #wrap_all .av-social-link-behance:hover a,
#top #wrap_all .av-social-link-behance a:focus{
	color: #fff;
	background-color: #008cfa;
}

#top #wrap_all .av-social-link-flickr:hover a,
#top #wrap_all .av-social-link-flickr a:focus{
	color: #fff;
	background-color: #ff0086;
}

#top #wrap_all .av-social-link-forrst:hover a,
#top #wrap_all .av-social-link-forrst a:focus{
	color: #fff;
	background-color: #234317;
}

#top #wrap_all .av-social-link-myspace:hover a,
#top #wrap_all .av-social-link-myspace a:focus{
	color: #fff;
	background-color: #000000;
}

#top #wrap_all .av-social-link-tumblr:hover a,
#top #wrap_all .av-social-link-tumblr a:focus{
	color: #fff;
	background-color: #345574;
}

#top #wrap_all .av-social-link-vimeo:hover a,
#top #wrap_all .av-social-link-vimeo a:focus{
	color: #fff;
	background-color: #31baff;
}

#top #wrap_all .av-social-link-youtube:hover a,
#top #wrap_all .av-social-link-youtube a:focus{
	color: #fff;
	background-color: #a72b1d;
}

#top #wrap_all .av-social-link-pinterest:hover a,
#top #wrap_all .av-social-link-pinterest a:focus{
	color: #fff;
	background-color: #cb2027;
}

#top #wrap_all .av-social-link-skype:hover a,
#top #wrap_all .av-social-link-skype a:focus{
	color: #fff;
	background-color: #12a5f4;
}

#top #wrap_all .av-social-link-instagram:hover a,
#top #wrap_all .av-social-link-instagram a:focus{
	color: #fff;
	background-color: #a67658;
}

#top #wrap_all .av-social-link-five_100_px:hover a,
#top #wrap_all .av-social-link-five_100_px a:focus{
	color: #fff;
	background-color: #222222;
}

#top #wrap_all .av-social-link-soundcloud:hover a,
#top #wrap_all .av-social-link-soundcloud a:focus{
	color: #fff;
	background-color: #F76700;
}

#top #wrap_all .av-social-link-xing:hover a,
#top #wrap_all .av-social-link-xing a:focus{
	color: #fff;
	background-color: #006567;
}

#top #wrap_all .av-social-link-vk:hover a,
#top #wrap_all .av-social-link-vk a:focus{
	color: #fff;
	background-color: #597BA5;
}

#top #wrap_all .av-social-link-reddit:hover a,
#top #wrap_all .av-social-link-reddit a:focus{
	color: #fff;
	background-color: #FF4500;
}

#top #wrap_all .av-social-link-telegram:hover a,
#top #wrap_all .av-social-link-telegram a:focus{
	color: #fff;
	background-color: #25A4E3;
}

#top #wrap_all .av-social-link-yelp:hover a,
#top #wrap_all .av-social-link-yelp a:focus{
	color: #fff;
	background-color: #d32323;
}

#top .av-section-bottom-logo .social_bookmarks,
.html_bottom_nav_header #top .av-logo-container .social_bookmarks{
	position: absolute;
	top: 50%;
	margin-top: -15px;
	right: 0;
}

.html_bottom_nav_header .main_menu .social_bookmarks{
	display:none
}


.html_cart_at_menu #top .av-section-bottom-logo .social_bookmarks,
.html_bottom_nav_header.html_cart_at_menu  #top .av-logo-container .social_bookmarks{
	left:0;
	right: auto;
}

#top .av-logo-container .social_bookmarks li{
	border:none;
}

#top .av-logo-container .social_bookmarks li a{
	border-radius: 100px;
}


/*first level*/
.sub_menu{
	float:left;
	z-index: 2;
	font-size: 11px;
	line-height: 30px;
	position: relative;
	top:10px;
}

#top .sub_menu>ul{
	float:left;
	margin:0;
}

#top .sub_menu>ul, #top .sub_menu>ul>li{
	background: transparent;
}

.sub_menu li{
	float:left;
	position: relative;
	padding:0 10px;
	border-right-style: solid;
	border-right-width: 1px;
	line-height: 10px;
}

.sub_menu>ul>li>a,
.sub_menu>div>ul>li>a{
	text-decoration: none;
	font-weight: bold;
	padding:7px 0;
}

/*second level*/
#top .sub_menu li ul{
	display: none;
	position: absolute;
	width:170px;
	padding:4px 0;
	z-index: 101;
	box-shadow: 0 8px 15px rgba(0,0,0,0.1);
	left:-50%;
	margin:0;
	border-style: solid;
	border-width: 1px;
	top:19px;
}

#top .sub_menu li:hover>ul{
	display:block;
}

.sub_menu>ul>li:hover>a{
	text-decoration: underline;
}

.sub_menu li li{
	float:none;
	line-height: 20px;
	border:none;
	padding: 0 0;
	margin:0;
}

#top .sub_menu li li a{
	width: 100%;
	height: auto;
	text-align: left;
	line-height: 23px;
	padding: 6px 18px;
	font-size: 12px;
	min-height: 23px;
	max-width: none;
	text-decoration: none;
	display: block;
	border-top-style: dashed;
	border-top-width: 1px;
}

#top .sub_menu li li:first-child > a {
	border: none;
}

#top .sub_menu li li a:hover{
	text-decoration: none;
	background: #f8f8f8;
}

/*third level and deeper*/
#top .sub_menu li li ul{
	top:-1px;
	left:-169px;
	background: none;
	padding: 0;
}

.pointer_arrow{
	border-style:solid;
	border-width:1px;
}


/* ======================================================================================================================================================
#Blog Styles default global stuff, always necessary. more style in the ALB blog module
====================================================================================================================================================== */

.content,
.sidebar{
	padding-top:50px;
	padding-bottom:50px;
	box-sizing: content-box;
	min-height: 1px;
	z-index: 1;
}

.content:hover,
.sidebar:hover{
	z-index: 1;
}

/*right sidebar - default*/
#top #main .sidebar{
	border-left-style:solid;
	border-left-width:1px;
	margin-left:0;
	float: none;
	width: auto;
	overflow: hidden;
	display: block;
	clear: none;
}

.inner_sidebar{
	margin-left:50px;
}

.content{
	border-right-style:solid;
	border-right-width:1px;
	margin-right:-1px;
}

.content .entry-content-wrapper{
	padding-right:50px;
}

/*left sidebar*/
#top #main .sidebar_left .sidebar{
	border-right-style:solid;
	border-right-width:1px;
	border-left:none;
}

.sidebar_left .inner_sidebar{
	margin-right:50px;
	margin-left:0;
}

.sidebar_left .content{
	float:right;
	border-left-style:solid;
	border-left-width:1px;
	border-right:none;
	margin-right:-50px;
	margin-left:-1px;
	padding-left:50px;
}


/*no sidebar*/
.fullsize .content{
	margin:0;
	border:none;
}

.fullsize .content .entry-content-wrapper{
	padding-right:0;
}


.container .minor-meta{
	font-size: 0.9em;
}

.post{
	clear:both;
	width:100%;
	float:left;
	position: relative;
}


.rounded-container,
.rounded-container img{
	border-radius: 111px;
	overflow: hidden;
	display: block;
	position: relative;
	z-index: 2;
}

.rounded-container{
	float:left;
	width:81px;
	height:81px;
	text-align: center;
	line-height: 81px;
}

.rounded-container .iconfont,
.small-preview .iconfont{
	font-size: 23px;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 1;
}


.entry-content-wrapper .post-title{
	font-size: 21px;
	line-height: 1.3em;
}


/*pagination*/

.pagination {
	clear:both;
	padding: 10px 0px 50px 0;
	position:relative;
	z-index:3;
	line-height: 13px;
	overflow: hidden;
}

.pagination span,
.pagination a {
	display:block;
	float:left;
	font-size:11px;
	line-height:13px;
	padding:2px 9px 1px 9px;
	text-decoration:none;
	width:auto;
}

#top .pagination .current,
#top .pagination a,
#top .fullsize .template-blog .pagination a{
	float: left;
	height: 35px;
	width: 35px;
	line-height: 35px;
	text-align: center;
	padding: 0;
	border-radius: 100px;
	margin-right: 3px;
	box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2);
}

.pagination .current{
	font-size:11px;
	padding:1px 9px 1px 9px;
	font-weight: bold;
}

.pagination .pagination-meta{
	float:right;
	line-height: 35px;
}


/*single post navigation*/

#top .avia-post-nav{
	position: fixed;
	height:110px;
	top:50%;
	background: #aaa;
	background: rgba(0,0,0,0.1);
	color:#fff;
	margin-top:-55px;
	padding:15px;
	text-decoration: none;
	z-index: 501; /*fixes: https://github.com/AviaThemes/wp-themes/issues/807 */
	transform: translate3d(0,0,0); /* fixes: https://kriesi.at/support/topic/next-previous-blog-post-bug/ */
}

#top .avia-post-nav:hover{
	background: #222;
	background: rgba(0,0,0,0.8);
}

.avia-post-nav .label{
	position: absolute;
	top:50%;
	height:22px;
	line-height: 22px;
	margin-top:-11px;
	font-size: 24px;
}

.avia-post-nav .entry-image{
	height:80px;
	width:80px;
	display:block;
}

.avia-post-nav .entry-image img{
	border-radius: 100px;
	display: block;
}

.avia-post-prev{
	left:0;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.avia-post-prev .label{
	left:10px;
}

.avia-post-next{
	right:0;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

.avia-post-next .label{
	right:10px;
}

.avia-post-next.with-image{
	text-align: right;
}

.avia-post-nav .entry-info{
	display:block;
	height:80px;
	width:220px;
	display: table;
}

.avia-post-next .entry-info{
	margin:0 20px 0 1px;
}

.avia-post-prev .entry-info{
	margin:0 1px 0 20px;
}

.avia-post-nav .entry-info span{
	display: table-cell;
	vertical-align: middle;
	font-size: 13px;
	line-height: 1.65em;
}
.avia-post-nav .entry-info-wrap{
	width:1px;
	overflow: hidden;
	display:block;
}

.avia-post-nav:hover .entry-info-wrap{
	width:240px;
}


/* page split pagination */
.pagination_split_post{
	clear: both;
	padding-top: 20px;
}


.no_sidebar_border#top #main .sidebar,
.no_sidebar_border .content{
	border-left:none;
	border-right:none;
}

.sidebar_shadow#top #main .sidebar,
.sidebar_shadow .content{
	border-left:none;
	border-right:none;
}

.sidebar_shadow#top #main .sidebar_right.av-enable-shadow{
	box-shadow: inset 25px 0 25px -25px #e9e9eb;
}

.sidebar_shadow .sidebar_right .content.av-enable-shadow{
	box-shadow: 25px 0 25px -25px #e9e9eb;
}

.sidebar_shadow#top #main .sidebar_left.av-enable-shadow{
	box-shadow: inset -25px 0 25px -25px #e9e9eb;
}

.sidebar_shadow .sidebar_left .content.av-enable-shadow{
	box-shadow: -25px 0 25px -25px #e9e9eb;
}


/* ======================================================================================================================================================
#Page Styles
====================================================================================================================================================== */

.template-page .entry-content-wrapper h1,
.template-page .entry-content-wrapper h2{
	text-transform: uppercase;
	letter-spacing: 1px;
}

.extra-mini-title{
	padding-bottom:20px;
}

.page-heading-container{
	position: relative;
	margin-bottom: 40px;
	padding: 0 0 44px 0;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	padding-right: 50px;
}

.fullsize .page-heading-container{
	padding-right:0;
}

.page-thumb img{
	border-radius: 3px;
}

/*template builder page styles*/
.template-page .template-blog .entry-content-wrapper h1,
.template-page .template-blog .entry-content-wrapper h2{
	text-transform: none;
	letter-spacing: 0;
}

.content .entry-content-wrapper .entry-content-wrapper{
	padding-right:0;
	padding-left:0;
}

.content .entry-content-wrapper .entry-content-wrapper .big-preview.single-big {
	padding: 0 0 10px 0;
}


/*search page*/
.template-search #searchform>div{
	max-width: 100%;
	margin-bottom:0;
}

#top .template-search.content .entry-content-wrapper {
	padding-bottom: 40px;
	padding-left:55px;
	font-size: 13px;
	clear:both;
}

.template-search .pagination {
	padding: 1px 50px 10px 55px;
}

.template-search .entry-content-wrapper .post-title {
	font-size:19px;
}

#top .template-search .entry-content-wrapper .post-title a:hover{
	text-decoration: underline;
}

.search-result-counter {
	position: absolute;
	left: 0;
	top: 1px;
	box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.2);
	height: 44px;
	line-height: 24px;
	padding: 10px;
	text-align: center;
	border-radius: 100px;
	width: 44px;
}

#top #search-fail{
	padding-left:0;
}

#top #search-fail #searchform{
	padding-bottom: 40px;
}

.template-search .post-entry{
	position: relative;
	clear:both;
}

.template-search .avia-content-slider.avia-content-grid-active .post-entry{
	clear: unset;
}


/*author page*/
.page-heading-container .author_description{
	overflow: hidden;
}

.template-author .content .post .entry-content-wrapper{
	padding-bottom:40px;
	font-size: 1em;
	line-height: 1.65em;
}

.template-author .pagination {
	padding: 1px 50px 10px 0;
}

.template-author .entry-content-wrapper .post-title {
	font-size:19px;
}

#top .template-author .entry-content-wrapper .post-title a:hover{
	text-decoration: underline;
}

.author-extra-border{
	display:block;
	position: absolute;
	bottom:-1px;
	width:600%;
	right:0;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

.fullsize .author-extra-border{
	right:auto;
	left:-100%
}

.template-author .post-entry{
	position: relative;
	clear:both;
}


/*archive page*/
.template-archives .tab_inner_content li {
	width: 48%;
	float: left;
	clear: none;
	margin:0 2% 0 0 ;
	list-style-position: inside;
}

.template-archives .relThumWrap img,
.template-archives .relThumWrap span{
	width:100%;
	text-decoration: none;
}

.template-archives .relThumbTitle{
	display: block;
	clear:both;
}


/*tag archive */
#top .fullsize .template-blog .tag-page-post-type-title {
	font-size: 50px;
	text-transform: uppercase;
}

.archive .category-term-description:empty{
	display:none;
}

.archive .category-term-description{
	margin-bottom: 25px;
}


/* ======================================================================================================================================================
#Widget & Sidebar - those are loaded in any case since the fallback widgets might be in use in the footer. if any other widgets are used the widget.css file
gets loaded
====================================================================================================================================================== */
.widgettitle{
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 1.1em;
}

.widget{
	clear:both;
	position: relative;
	padding:30px 0 30px 0;
	float:none;
}

#footer .widget{
	padding:0;
	margin:30px 0 30px 0;
	overflow: hidden;
}

#top .widget ul{
	padding:0;
	margin:0;
	width:100%;
	float:left;
}

#top #footer .widget ul{
	float:none
}

.widget li{
	clear:both;
}

.widget ul ul li,
.widget ul ol li,
.widget ol ol li,
.widget ol ul li{
	margin-bottom:0;
}


/*direct mailchimp form embeds: show the button which has a clear attribute*/
#mc_embed_signup .clear{
	visibility: visible;
	overflow: visible;
	height:auto;
}


/* ======================================================================================================================================================
#Footer & #Socket
====================================================================================================================================================== */

#footer{
	padding: 15px 0 30px 0;
	z-index: 1;
}

#socket .container{
	padding-top: 15px;
	padding-bottom: 15px;
}

#socket{
	font-size: 11px;
	margin-top: -1px;
	z-index: 1;
}

#socket .menu{
	margin-top: 6px;
}

#socket .sub_menu_socket{
	float: right;
	margin: 0;
}

#socket .sub_menu_socket div{
	overflow: hidden;
}

#socket .sub_menu_socket li{
	float: left;
	display: block;
	padding: 0 10px;
	border-left-style: solid;
	border-left-width: 1px;
	line-height: 10px;
}

#socket .sub_menu_socket li:first-child{
	border:none;
}

#socket .sub_menu_socket li:last-child{
	padding-right:0;
}

#socket .copyright{
	float:left;
}

/*	Curtain effect	*/
.av-curtain-footer #av-curtain-footer-placeholder{
	display: none;
	pointer-events: none;
}

.av-curtain-footer .av-curtain-footer-container{
	position: relative;
	float: left;
	width: 100%;
}

.html_header_sidebar.html_header_left .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
	width: calc(100% - 300px);
	margin-left: 301px;
}

.html_header_sidebar.html_header_right .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
	width: calc(100% - 301px);
	margin-left: 0;
}

/*	activate curtain effect on screen width	*/
@media only screen and (min-width: 990px)
{
	.av-curtain-footer.av-curtain-medium #av-curtain-footer-placeholder{
		clear: both;
		background: transparent;
		display: block;
	}
	.av-curtain-footer.av-curtain-medium #main > *:not(.av-curtain-footer-container){
		z-index: 1;
		position: relative;
	}

	.av-curtain-footer.av-curtain-medium .av-curtain-footer-container{
		width: 100%;
		display: block;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 0;
	}
}

@media only screen and (min-width: 769px)
{
	.av-curtain-footer.av-curtain-small #av-curtain-footer-placeholder{
		clear: both;
		background: transparent;
		display: block;
	}
	.av-curtain-footer.av-curtain-small #main > *:not(.av-curtain-footer-container){
		z-index: 1;
		position: relative;
	}

	.av-curtain-footer.av-curtain-small .av-curtain-footer-container{
		width: 100%;
		display: block;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 0;
	}
}

@media only screen and (min-width: 480px)
{
	.av-curtain-footer.av-curtain-mini #av-curtain-footer-placeholder{
		clear: both;
		background: transparent;
		display: block;
	}
	.av-curtain-footer.av-curtain-mini #main > *:not(.av-curtain-footer-container){
		z-index: 1;
		position: relative;
	}

	.av-curtain-footer.av-curtain-mini .av-curtain-footer-container{
		width: 100%;
		display: block;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 0;
	}
}

/*	activate curtain effect with js	*/
.av-curtain-footer.av-curtain-activated #av-curtain-footer-placeholder{
	clear: both;
	background: transparent;
	display: block;
}

.av-curtain-footer.av-curtain-activated #main > *:not(.av-curtain-footer-container){
	z-index: 1;
	position: relative;
}

.av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
	width: 100%;
	display: block;
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 0;
}

#scroll-top-link{
	position: fixed;
	border-radius: 2px;
	height: 50px;
	width: 50px;
	line-height: 50px;
	text-decoration: none;
	text-align: center;
	opacity: 0;
	right: 50px;
	bottom: 50px;
	z-index: 1030;
	visibility: hidden;
}

#av-cookie-consent-badge{
	position: fixed;
	border-radius: 2px;
	height: 30px;
	width: 30px;
	line-height: 30px;
	text-decoration: none;
	text-align: center;
	opacity: 0;
	right: 50px;
	bottom: 50px;
	z-index: 1030;
	visibility: hidden;
}

#scroll-top-link.avia_pop_class,
#av-cookie-consent-badge.avia_pop_class{
	opacity: 0.7;
	visibility: visible;
}

#socket .social_bookmarks{
	float: right;
	margin: -10px 0 0 30px;
	position: relative;
}

#socket .social_bookmarks li{
	border-radius: 300px;
	border: none;
	overflow: hidden;
	top: 5px;
	position: relative;
}

#socket .social_bookmarks li a{
	border-radius: 300px;
}
#socket .avia-bullet,
#socket .avia-menu-fx{
	display: none;
}


/* ======================================================================================================================================================
#CSS ANIMATION
====================================================================================================================================================== */

.small-preview,
.avia-post-nav .entry-info-wrap,
.avia-post-nav,
.avia-menu-fx,
.team-social,
.button,
.related-format-icon,
.avia-slideshow-controls a,
#top .social_bookmarks li a,
.fallback-post-type-icon,
#scroll-top-link,
#av-cookie-consent-badge,
.avia-slideshow-button{
	transition: all 0.3s ease-out;
}

.main_menu a,
.pagination a{
	transition: color 0.15s ease-out;
	transition: background 0.15s ease-out;
}

.avia_pop_class,
.avia-search-tooltip{
	animation: avia_pop 0.3s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275); /* IE 10+ */
}

a:hover .image-overlay .image-overlay-inside{
	animation: avia_pop_small 0.5s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
}

.av-post-swiped-overlay{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 1;
	z-index: 100;
	background-color: #fff;
}

.av-post-swiped-overlay.swiped-ltr{
	animation: avia_swipe_ltr 3s 1 cubic-bezier(0.17,0.84,0.44,1);
	width: 0;
	left: 100%;
}

.av-post-swiped-overlay.swiped-rtl{
	animation: avia_swipe_rtl 3s 1 cubic-bezier(0.17,0.84,0.44,1);
	width: 0;
}

@keyframes avia_swipe_ltr{
	0%	{ width: 100%; left: 0; }
	100%{ width: 100%; left: 100%; }
}

@keyframes avia_swipe_rtl{
	0%	{ width: 100%; left: 0; }
	100%{ width: 100%; left: -100%; }
}

@keyframes avia_pop {
  0%   { transform:scale(0.8);  }
  100% { transform:scale(1);   }
}

@keyframes avia_pop_small {
  0%   { transform:rotate(-175deg) scale(0.2);  }
  100% { transform:rotate(0deg) scale(1);   }
}

@keyframes avia_pop_loader {
  0%   { transform: rotate(0deg) scale(0.2);  }
  100% { transform: rotate(720deg) scale(1);   }
}

@keyframes avia_shrink {
  0% { opacity:0; transform: scale(1); }
  75% {opacity:0.7; }
  100% { opacity:0; transform: scale(0);}
}

@keyframes av-load8 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



/* #Media Queries
================================================== */

/* large screens with 1140px width */

@media only screen and (min-width: 1140px)  {

}


/* screens with 1024px width */

@media only screen and (max-width: 1024px) {
	#top .socket_color, 
	#top .footer_color,
	#top .header_color .header_bg,
	#top .main_color,
	#top .alternate_color {
		background-attachment: scroll;
	}
}

/* Smaller than standard 960 (devices and browsers) */
@media only screen and (max-width: 989px)
{
	.responsive.html_header_sidebar #top #header{
		width:27%
	}

	.responsive.html_header_left #main {
		margin-left: 27%;
	}

	.responsive.html_header_right #main {
		margin-right: 27%;
	}

	.responsive.html_header_sidebar #header .av-main-nav{
		padding: 4% 0;
	}

	.responsive.html_header_sidebar #header .av-main-nav > li {
		margin: 0 10%;
	}

	.responsive.html_header_sidebar #header .av-main-nav > li > a .avia-menu-text{
		font-size: 14px;
	}

	.responsive.html_header_sidebar #header .av-main-nav > li > a .avia-menu-subtext{
		font-size: 11px;
	}

	.responsive.html_header_sidebar #header .avia-custom-sidebar-widget-area .widget{
		padding:10%;
	}

	.responsive.html_header_sidebar .logo{
		padding: 10%;
	}

	/*headers*/
	.responsive.html_mobile_menu_tablet #top .av_header_transparency.av_alternate_logo_active .logo a > img,
	.responsive.html_mobile_menu_tablet #top .av_header_transparency.av_alternate_logo_active .logo a > svg{
		opacity: 1
	}

	.responsive.html_mobile_menu_tablet #top .av_header_transparency .logo img.alternate,
	.responsive.html_mobile_menu_tablet #top .av_header_transparency .logo .subtext.avia-svg-logo-sub{
		display: none;
	}

	/* curtain footer */
	.responsive.html_header_sidebar.html_header_left .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
		width: calc(73%);
		margin-left: calc(27% + 1px);
	}

	.responsive.html_header_sidebar.html_header_right .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
		width: calc(73% - 1px);
		margin-left: 0;
	}

	/*framed layout*/
	.responsive .av-frame{
		display:none;
	}

	.responsive.html_av-framed-box{
		padding:0;
	}

	.responsive.html_header_top.html_header_sticky.html_av-framed-box #header_main,
	.responsive.html_header_top.html_header_sticky.html_av-framed-box #header_meta{
		margin:0 auto;
	}

	.responsive #top .avia-post-prev {
		left: 0px;
	}
	.responsive #top .avia-post-next {
		right: 0px;
	}

	.responsive.html_av-framed-box.html_av-overlay-side .av-burger-overlay-scroll{
		right:0
	}

	/* cookie consent */
	.responsive .avia-cookie-consent .container{
		padding: 0;
	}

	.responsive .avia-cookie-consent a.avia_cookie_infolink,
	.responsive .avia-cookie-consent p {
		display: block;
		margin-right: 0;
	}

	.responsive .avia-cookie-consent-button{
		margin: 0.5em;
	}

	.responsive .av-framed-box .avia-cookiemessage-top,
	.responsive .av-framed-box .avia-cookiemessage-bottom {
		width: 100% !important;
		left: 0 !important;
	}

	.responsive .av-framed-box .avia-cookiemessage-bottom{
		bottom: 0 !important;
	}

	.responsive .av-framed-box .avia-cookiemessage-top{
		top: 0 !important;
	}

	.responsive .avia-cookiemessage-top-left,
	.responsive .avia-cookiemessage-bottom-left,
	.responsive .avia-cookiemessage-top-right,
	.responsive .avia-cookiemessage-bottom-right{
		width: 35%;
	}

}

/* Tablet Portrait size to standard 960 (devices and browsers) */
@media only screen and (min-width: 768px) and (max-width: 989px)
{
	.responsive .main_menu ul:first-child > li > a {
		padding: 0 10px;
	}

	.responsive #top .header_bg {
		opacity: 1;
	}

	.responsive #main .container_wrap:first-child{
		border-top:none;
	}

	.responsive .logo{
		float:left;
	}

	.responsive .logo img{
		margin:0;
	}

	.responsive.html_top_nav_header.html_mobile_menu_tablet #top .social_bookmarks {
		right: 50px;
	}

	.responsive.html_top_nav_header.html_mobile_menu_tablet #top #wrap_all #main{
		padding-top:0;
	}

	.responsive.js_active .avia_combo_widget .top_tab .tab{
		font-size: 10px;
	}

	.responsive.js_active .avia_combo_widget .news-thumb{
		display:none;
	}

	.responsive #top #wrap_all .grid-sort-container .av_one_sixth{
		width:33.3%;
		margin-bottom: 0;
	}

	.responsive body.boxed#top,
	.responsive.html_boxed.html_header_sticky #top #header,
	.responsive.html_boxed.html_header_transparency #top #header{
		max-width: 100%;
	}

	.responsive.html_header_top.html_header_sticky.html_bottom_nav_header.html_mobile_menu_tablet #main{
		padding-top: 88px;
	}

	.responsive.html_header_top.html_header_sticky.html_bottom_nav_header.html_top_nav_header.html_mobile_menu_tablet #main{
		margin-top:0;
	}

	.responsive #top .av-hide-on-tablet{
		display:none !important;
	}

	/*new menu*/
	.responsive.html_mobile_menu_tablet .av-burger-menu-main{
		display: block;
	}

	.responsive #top #wrap_all .av_mobile_menu_tablet .main_menu{
		top:0;
		left:auto;
		right:0;
		display:block;
	}

	.responsive.html_logo_right #top #wrap_all .av_mobile_menu_tablet .main_menu{
		top:0;
		left:0;
		right:auto;
	}

	.responsive #top .av_mobile_menu_tablet .av-main-nav .menu-item{
		display:none;
	}

	.responsive #top .av_mobile_menu_tablet .av-main-nav .menu-item-avia-special{
		display:block;
	}

	.responsive #top #wrap_all .av_mobile_menu_tablet .menu-item-search-dropdown > a {
		font-size: 24px;
	}

	.responsive #top .av_mobile_menu_tablet #header_main_alternate{
		display:none;
	}

	.responsive.html_mobile_menu_tablet #top #wrap_all #header {
		position: relative;
		width:100%;
		float:none;
		height:auto;
		margin:0 !important;
		opacity: 1;
		min-height:0;
	}

	.responsive.html_mobile_menu_tablet #top #header #header_meta .social_bookmarks{
		display:none;
	}

	.responsive.html_mobile_menu_tablet #top .av-logo-container .social_bookmarks{
		display:none
	}

	.responsive.html_mobile_menu_tablet #top .av-logo-container .main_menu .social_bookmarks{
		display:block;
		position: relative;
		margin-top: -15px;
		right:0;
	}

	.responsive.html_logo_center.html_bottom_nav_header .av_mobile_menu_tablet .avia-menu.av_menu_icon_beside{
		height:100%;
	}

	.responsive.html_mobile_menu_tablet #top #wrap_all .menu-item-search-dropdown > a {
		font-size: 24px;
	}

	.responsive.html_mobile_menu_tablet #top #main .av-logo-container .main_menu{
		display:block;
	}

	.responsive.html_mobile_menu_tablet.html_header_top.html_header_sticky #top #wrap_all #main{
		padding-top: 88px;
	}

	.responsive.html_mobile_menu_tablet.html_header_top #top #main {
		padding-top: 0 !important;
		margin: 0;
	}

	.responsive.html_mobile_menu_tablet.html_top_nav_header.html_header_sticky #top #wrap_all #main{
		padding-top:0;
	}

	.responsive.html_mobile_menu_tablet #top #header_main > .container .main_menu  .av-main-nav > li > a,
	.responsive.html_mobile_menu_tablet #top #wrap_all .av-logo-container {
		height:90px;
		line-height:90px;
	}

	.responsive.html_mobile_menu_tablet #top #header_main > .container .main_menu  .av-main-nav > li > a{
		min-width: 0;
		padding:0 0 0 20px;
		margin:0;
		border-style: none;
		border-width: 0;
	}

	.responsive.html_mobile_menu_tablet #top .av_seperator_big_border .avia-menu.av_menu_icon_beside{
		border-right-style: solid;
		border-right-width: 1px;
		padding-right: 25px;
	}

	.responsive.html_mobile_menu_tablet #top #header .av-main-nav > li > a, .responsive #top #header .av-main-nav > li > a:hover{
		background:transparent;
		color: inherit;
	}

	.responsive.html_mobile_menu_tablet.html_top_nav_header .av-logo-container .inner-container{
		overflow: visible;
	}

}

/* All Mobile Sizes (devices and browser) */
@media only screen and (max-width: 767px)
{
	/*blog*/
	.responsive .template-archives .tab_inner_content li{
		width: 98%;
	}

	.responsive .template-blog .blog-meta,
	.responsive .post_author_timeline,
	.responsive #top #main .sidebar{
		display: none;
	}

	/*	@since 4.9 https://kriesi.at/support/topic/remove-sidebar-from-blog-on-mobile/	*/
	.sidebar_shadow#top #main .sidebar_right.av-enable-shadow,
	.sidebar_shadow#top #main .sidebar_left.av-enable-shadow,
	.sidebar_shadow .sidebar_right .content.av-enable-shadow,
	.sidebar_shadow .sidebar_left .content.av-enable-shadow{
		box-shadow: none;
	}

	.responsive #top #main .sidebar.smartphones_sidebar_active{
		display: block;
		text-align: left;
		border-left: none;
		border-right: none;
		border-top-style: dashed;
		border-top-width: 1px;
		width: 100%;
		clear: both;
	}

	.responsive #top #main .sidebar.smartphones_sidebar_active .inner_sidebar{
		margin: 0;
	}

	.responsive .content .entry-content-wrapper{
		padding:0;
	}

	.responsive .content{
		border:none;
	}

	.responsive .template-blog .post_delimiter {
		margin: 0 0 30px 0;
		padding: 30px 0 0 0;
	}

	.responsive .big-preview{
		padding: 0 0 10px 0;
	}

	.responsive .related_posts{
		padding:20px 0;
	}

	.responsive .comment_content{
		padding-right:0;
		padding-left:0;
	}

	.responsive .fullsize div .template-blog .entry-content-wrapper{
		text-align: left;
		font-size:14px;
		line-height: 24px;
	}

	.responsive #top .fullsize .template-blog .post .entry-content-wrapper > * {
		max-width: 100%;
	}

	.responsive #top .avia-post-nav {
		display: none;
	}

	.responsive #top .av-related-style-full .no_margin.av_one_half.relThumb{
		display: block;
		width:100%;
		clear:both;
	}

	/*all templates*/
	.responsive .title_container .breadcrumb{
		left:-2px;
	}

	.responsive .title_container .main-title + .breadcrumb{
		position: relative;
		right:auto;
		top:-6px;
		margin:0;
		left:-2px;
	}

	.responsive .pagination{
		padding-left: 0;
		padding-right: 0;
	}
	.responsive #top .av-hide-on-mobile,
	.responsive #top .av-hide-on-tablet{
		display:none !important;
	}


	/*header*/
	.responsive #top .av_header_transparency.av_alternate_logo_active .logo a > img,
	.responsive #top .av_header_transparency.av_alternate_logo_active .logo a > svg{
		opacity: 1
	}

	.responsive #top .av_header_transparency .logo img.alternate,
	.responsive #top .av_header_transparency .logo .subtext.avia-svg-logo-sub{
		display: none;
	}

	.responsive #top #wrap_all #header {
		position: relative;
		width:100%;
		float:none;
		height:auto;
		margin:0 !important;
		opacity: 1;
		min-height:0;
	}

	.responsive #top #main {
		padding-top:0 !important;
		margin:0;
	}

	.responsive #top #main .container_wrap:first-child{
		border-top:none;
	}

	.responsive.html_header_top.html_logo_center .logo {
		left: 0%;
		transform: translate(0%, 0);
		margin:0;
	}

	.responsive .phone-info{
		float:none;
		width:100%;
		clear:both;
		text-align: center;
	}

	.responsive .phone-info div{
		margin:0;
		padding:0;
		border:none;
	}

	.responsive.html_header_top #header_main .social_bookmarks,
	.responsive.html_top_nav_header #top .social_bookmarks {
		width:auto;
		margin-top:-16px;
	}

	.responsive #top .logo{
		position: static;
		display:table;
		height:80px !important;
		float:none;
		padding:0;
		border:none;
		width:80%;
	}

	.responsive .logo a{
		display:table-cell;
		vertical-align: middle;
	}

	.responsive .logo img,
	.responsive .logo svg{
		height: auto !important;
		width: auto;
		max-width: 100%;
		display: block;
		max-height: 80px;
	}

	.responsive #header_main .container{
		height:auto !important;
	}

	.responsive #top .header_bg {
		opacity: 1;
	}

	.responsive.social_header .phone-info {
		text-align: center;
		float:none;
		clear:both;
		margin:0;
		padding:0;
	}

	.responsive.social_header .phone-info div{
		border:none;
		width:100%;
		text-align: center;
		float:none;
		clear:both;
		margin:0;
		padding:0;
	}

	.responsive #header_meta .social_bookmarks li{
		border-style:solid;
		border-width:1px;
		margin-bottom:-1px;
		margin-left:-1px;
	}

	.responsive #top #header_meta .social_bookmarks li:last-child{
		border-right-style: solid;
		border-right-width:  1px;
	}

	.responsive #header .sub_menu,
	.responsive #header_meta .sub_menu>ul{
		float:none;
		width:100%;
		text-align: center;
		margin:0 auto;
		position: static;
	}

	.responsive #header .social_bookmarks{
		padding-bottom:2px;
		width:100%;
		text-align: center;
		height:auto;
		line-height: 0.8em;
		margin:0;
	}

	.responsive #header_meta .sub_menu>ul>li{
		float:none;
		display: inline-block;
		padding: 0 10px;
	}

	.responsive #header .social_bookmarks li{
		float:none;
		display: inline-block;
	}

	.responsive.bottom_nav_header #header_main .social_bookmarks{
		position: relative;
		top: 0;
		right: 0;
		margin: 10px auto;
		clear:both;
	}

	.responsive.bottom_nav_header.social_header .main_menu>div{
		height:auto;
	}

	.responsive .logo img,
	.responsive .logo svg{
		margin: 0;
	}
	.responsive.html_header_sidebar #top #header .social_bookmarks{
		display:none;
	}

	.responsive body.boxed#top,
	.responsive.html_boxed.html_header_sticky #top #header{
		max-width: 100%;
	}

	.responsive.html_header_transparency #top .avia-builder-el-0 .container,
	.responsive.html_header_transparency #top .avia-builder-el-0 .slideshow_inner_caption{
		padding-top:0;
	}

	.responsive #top .av_phone_active_right .phone-info.with_nav span{
		border:none;
	}

	.responsive #top #wrap_all .av_header_transparency .main_menu ul:first-child > li > a,
	.responsive #top #wrap_all .av_header_transparency .sub_menu > ul > li > a,
	.responsive #top .av_header_transparency #header_main_alternate,
	.responsive .av_header_transparency #header_main .social_bookmarks li a,
	.responsive #top #wrap_all .av_header_transparency .phone-info.with_nav span,
	.responsive #top .av_header_transparency #header_meta,
	.responsive #top .av_header_transparency #header_meta li,
	.responsive #top #header_meta .social_bookmarks li a{
		color:inherit;
		border-color: inherit;
		background: inherit;
	}

	.responsive.html_top_nav_header .av-logo-container{
		height:auto;
	}

	.responsive.html_top_nav_header .av-section-bottom-logo{
		border-bottom-style: solid;
		border-bottom-width: 1px;
	}

	/*new mobile*/
	.responsive .av-burger-menu-main{
		display: block;
	}

	.responsive #top #wrap_all .main_menu{
		top:0;
		height:80px;
		left:auto;
		right:0;
		display: block;
		position: absolute;
	}

	.responsive .main_menu ul:first-child > li a {
		height: 80px;
		line-height: 80px;
	}

	.responsive #top .av-main-nav .menu-item{
		display:none;
	}

	.responsive #top .av-main-nav .menu-item-avia-special{
		display:block;
	}

	.responsive #top #wrap_all .menu-item-search-dropdown > a {
		font-size: 24px;
	}

	.responsive #header_main_alternate{
		display:none;
	}

	.responsive #top #header .social_bookmarks{
		display:none;
	}

	.responsive #top #header .main_menu .social_bookmarks{
		display:block;
		position: relative;
		margin-top: -15px;
	}

	.responsive #top .av-logo-container .avia-menu{
		height:100%;
	}

	.responsive #top .av-logo-container .avia-menu > li > a{
		line-height: 80px;
	}

	.responsive #top #main .av-logo-container .main_menu{
		display:block;
	}

	.responsive #top #main .av-logo-container .social_bookmarks{
		display:none;
	}

	.responsive #top #main .av-logo-container .main_menu .social_bookmarks{
		display:block;
		position: relative;
	}

	.responsive #top #main .av-logo-container .main_menu{
		display:block;
	}

	.responsive #top #header_main > .container .main_menu  .av-main-nav > li > a,
	.responsive #top #wrap_all .av-logo-container {
		height:80px;
		line-height:80px;
	}

	.responsive #top #wrap_all .av-logo-container {
		padding:0;
	}

	.responsive #top #header_main > .container .main_menu  .av-main-nav > li > a{
		min-width: 0;
		padding:0 0 0 20px;
		margin:0;
		border-style: none;
		border-width: 0;
	}

	.responsive #top .av_seperator_big_border .avia-menu.av_menu_icon_beside{
		border-right-style: solid;
		border-right-width: 1px;
		padding-right: 25px;
	}

	.responsive #top #header .av-main-nav > li > a, .responsive #top #header .av-main-nav > li > a:hover{
		background:transparent;
		color: inherit;
	}

	.responsive.html_top_nav_header .av-logo-container .inner-container{
		overflow: visible;
	}

	/*related images*/
	.responsive #top .related_entries_container .av_one_eighth{
		width:25%;
	}

	.responsive #top .relThumb5{
		clear:both;
	}

	.responsive.html_header_transparency #top .avia-builder-el-0 .container{
		padding-top:0px;
	}

	.responsive.html_header_sidebar #header .avia-custom-sidebar-widget-area{
		display:none;
	}

	.responsive.html_header_sidebar #main{
		border: none;
	}

	/*tabs*/
	.responsive.js_active #top .avia_combo_widget .top_tab .tab{
		border-top: 1px solid;
		border-bottom:none;
		width: 100%;
	}

	.responsive.js_active #top .avia_combo_widget .news-wrap li{
		padding:5px;
	}

	/*widgets*/
	.tagcloud a{
		padding:8px 20px;
		margin:0 8px 8px 0;
	}

	.widget li{
		line-height: 1.8em;
		font-size: 15px;
	}

	/*footer*/
	.responsive #scroll-top-link{
		display: none; /*iphones etc scroll better by tapping the status bar at the top of the screen*/
	}

	.responsive #socket .sub_menu_socket{
		display: block;
		float: none;
		width: 100%;
		clear: both;
		margin: 0 0 0 -15px;
	}

	/* curtain footer */
	.responsive.html_header_sidebar.html_header_left  .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
		width: 100%;
		margin-left: 0;
	}
	
	.responsive.html_header_sidebar.html_header_right  .av-curtain-footer.av-curtain-activated .av-curtain-footer-container{
		width: 100%;
	}

	.responsive.html_top_nav_header.av-burger-overlay-active #top #wrap_all #header {
		z-index: 0;
	}

	.responsive.html_top_nav_header.av-burger-overlay-active .av-curtain-footer.av-curtain-activated #main>.av-section-bottom-logo {
		z-index: 100;
	}

	/* cookie consent */
	body.responsive.admin-bar .avia-cookiemessage-top, body.responsive.admin-bar .avia-cookiemessage-top-left,
	body.responsive.admin-bar .avia-cookiemessage-top-right{
		margin-top: 46px;
	}

	.responsive .avia-cookiemessage-top-left, .responsive .avia-cookiemessage-bottom-left, .responsive .avia-cookiemessage-top-right,
	.responsive .avia-cookiemessage-bottom-right{
		width: 55%;
	}

}


/* Mobile Landscape Size to Tablet Portrait (devices and browsers) */
@media only screen and (min-width: 480px) and (max-width: 767px)
{
	/*portfolio*/
	.responsive #top #wrap_all .grid-sort-container.grid-total-odd .grid-entry.grid-loop-1{
		width:100%;
	}

	.responsive #top #wrap_all .grid-sort-container .grid-entry{
		width:50%;
		margin-bottom: 0;
	}

	.responsive #top #wrap_all .portfolio-parity-odd{
		clear:both;
	}
}

/* Mobile Portrait Size to Mobile Landscape Size (devices and browsers) */
@media only screen and (max-width: 479px)
{
	/*related images*/
	.responsive #top .related_entries_container .av_one_eighth{
		width:50%;
	}

	.responsive #top .related_entries_container .av_one_eighth:nth-child(odd){
		clear:both;
	}

	.responsive.html_header_top #header_main .social_bookmarks,
	.responsive.html_top_nav_header .social_bookmarks{
		display:none;
	}

	/*new mobile header*/
	.responsive .avia-menu.av_menu_icon_beside{
		padding:0;
		margin:0;
		border:none;
	}

	.responsive #top #wrap_all #header .social_bookmarks,
	.responsive #top #wrap_all #main .av-logo-container .social_bookmarks{
		display:none;
	}

	.responsive #top .av_seperator_big_border .avia-menu.av_menu_icon_beside{
		margin-right:0;
		padding-right:0;
		border:none;
	}

	/* cookie consent */
	.responsive .avia-cookiemessage-top-left,
	.responsive .avia-cookiemessage-bottom-left,
	.responsive .avia-cookiemessage-top-right,
	.responsive .avia-cookiemessage-bottom-right{
		width: 85% !important;
		left: 7.5% !important;
		right: 7.5% !important;
	}
}

/*	https://kriesi.at/support/topic/open-street-map-marker-line-brakes  */
.leaflet-popup-content br:nth-child(even) {
	display: none !important;
}
