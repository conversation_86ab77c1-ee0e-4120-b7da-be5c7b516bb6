

/* ======================================================================================================================================================
Horizontal Gallery
====================================================================================================================================================== */

.av-horizontal-gallery{
	clear:both;
	position: relative;
	z-index: 1;
	overflow: hidden;
}


.av-horizontal-gallery-inner{
	position: relative;
	white-space: nowrap;
	opacity: 0;
}

.av-horizontal-gallery-slider{
	left:0;
	position: absolute;
	height:100%;
	width:100%;
}

.av-horizontal-gallery-wrap{
	height:100%;
	top:0;
	display: inline-block;
	position: relative;
	z-index: 1;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	cursor: pointer;
	vertical-align: top;
}

.av-horizontal-gallery-animated .av-horizontal-gallery-inner{
	transition: opacity 0.4s ease-in-out;
	opacity: 1;
}

.av-horizontal-gallery-animated .av-horizontal-gallery-slider{
	transition: all 0.4s ease-in-out;
}

.av-horizontal-gallery-animated .av-horizontal-gallery-wrap{
	transition: all 0.4s ease-in-out;
}

.av-horizontal-gallery-wrap:after{
	height: 100%;
	width: 100%;
	position: absolute;
	content: "";
	top: 0;
	left: 0;
	z-index: 5;
}

.av-horizontal-gallery-enlarge-effect.av-horizontal-gallery{
	padding: 60px 0;
}

.av-horizontal-gallery-enlarge-effect .av-horizontal-gallery-wrap.av-active-gal-item{
	z-index: 2;
	transform: scale(1.3);
}

.av-horizontal-gallery-wrap:first-child{
	transform-origin: 0% 50%;
}

.av-horizontal-gallery-wrap:last-child{
	transform-origin: 100% 50%;
}

.av-horizontal-gallery-wrap.av-active-gal-item:after{
	display: none;
}

.av-horizontal-gallery-link{
	position: absolute;
	opacity: 0;
	bottom: 10px;
	right: 10px;
	transition: opacity 0.4s ease-in-out;
	z-index: 100;
}

.av-horizontal-gallery-wrap:hover .av-horizontal-gallery-link{
	opacity: 1;
}

.av-horizontal-gallery-img{
	height: 100%;
    width: auto;
    display: block
}

.av-horizontal-gallery-large-gap .av-horizontal-gallery-wrap{
	margin-right: 15px;
}

.av-horizontal-gallery-1px-gap .av-horizontal-gallery-wrap{
	margin-right: 1px;
}

#top .av-horizontal-gallery-link {
	color: #fff;
	background: rgba(0,0,0,0.3);
	display: block;
	line-height: 24px;
	width: 24px;
	text-align: center;
	border-radius: 3px;
	text-decoration: none;
	transition: all 0.2s ease-in-out;
}

#top .av-horizontal-gallery-link:hover{
	transform: scale(1.3);
}

/* Mobile Landscape Size to Tablet Portrait (devices and browsers) */
@media only screen and (max-width: 767px)
{
/*	.responsive #top .av-horizontal-gallery .avia-slideshow-controls a{
		display: block;		removed 5.3
	}*/

	.responsive .av-horizontal-gallery-inner{
		position: static;
		min-height: 180px;
	}

	.responsive .av-horizontal-gallery-enlarge-effect .av-horizontal-gallery-wrap.av-active-gal-item{
		transform: scale(1);
	}

	.responsive .av-horizontal-gallery-wrap{ max-width: 85vw; margin:0; overflow: hidden;}
	.responsive .av-horizontal-gallery-img{
		height: auto;
		width: 100%;
		transform: translate(0, -50%);
		top: 50%;
		position: relative;
	}

	.responsive .av-horizontal-gallery-fullwidth .av-horizontal-gallery-wrap{
		max-width: 100vw;
	}

	/* @since 5.3:  fix % gallery height covering images   */
	.responsive .av-horizontal-gallery .av-image-copyright{
		bottom: 15%;
	}

	.responsive .av-horizontal-gallery .av-copyright-left{
		left: 15%;
	}

	.responsive .av-horizontal-gallery .av-copyright-right{
		right: 15%;
	}
}
