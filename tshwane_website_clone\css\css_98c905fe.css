/* ======================================================================================================================================================
Tab Section and Slideshow / Tab Section
====================================================================================================================================================== */
.av-inner-tab-title,
.av-tab-section-icon,
.av-tab-arrow-container,
.av-tab-section-image{
	width: 90px;
}

.av-tab-section-container{
	width: 100%;
	overflow: hidden;
	position: relative;
	border: none;
}

.av-tab-section-outer-container{
	overflow: hidden;
}

.av-tab-section-inner-container{
	display: table;
	position: relative;
	left: 0%;

	/* Safari pixel gap fix */
	margin-left: -1px;
	margin-right: -1px;
}

.av-layout-tab{
	width: 100vw;
	display: block;
	vertical-align: middle;
	padding: 50px 0;
	border-top-style: solid;
	border-top-width: 1px;
}

.js_active .av-layout-tab{
	display: table-cell;
	border: none;
}

.av-tab-section-tab-title-container{
	text-align: center;
	padding: 20px 0 0 0;
	display: none;
	position: relative;
	transition: all 0.4s ease-in-out;
}

.js_active .av-tab-section-tab-title-container{
	display: block;
}

.js_active .av-tab-section-container.av-minimum-height .container{
	opacity: 1;
}

#top .av-section-tab-title{
	padding: 10px 20px 0px 20px;
	display: inline-block;
	text-decoration: none;
}

.av-section-tab-title,
.av-section-tab-title:hover{
	transition: all 0.4s ease-in-out;
}

.av-outer-tab-title{
	display: block;
}

.av-inner-tab-title{
	text-transform: uppercase;
	display: block;
	line-height: 1.2em;
	margin-top: 7px;
	margin-bottom: 3px;
	font-size: 13px;
	text-align: center;
}


.av-tab-with-image .av-inner-tab-title{
	display: table-cell;
	vertical-align: middle;
	height: 40px;
}

.av-tab-section-icon{
	display: inline-block;
    font-size: 40px;
    line-height: 1em;
}

.av-tab-arrow-container{
	display: block;
	height: 15px;
	overflow: hidden;
	position: relative;
	top: 8px;
}

.avia_transform .av-tab-arrow-container span{
	position: absolute;
	left: 50%;
	width: 50px;
	height: 50px;
	transform: translateX(-50%) rotate(45deg);
	transition: all 0.4s ease-in-out;
	top: 40px;
}

.avia_transform .av-active-tab-title .av-tab-arrow-container span{
	top: 10px;
}

.av-tab-section-image{
	display: block;
	opacity: 0.65;
	filter: grayscale(1);
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	height:90px;
}

.av-section-tab-title:hover .av-tab-section-image{
	opacity: 0.85;
}

.av-active-tab-title .av-tab-section-image{
	opacity: 1;
	filter: grayscale(0);
}

.av-tab-image-circle .av-tab-section-image{
	border-radius: 200px;
}

.av-tab-image-rounded .av-tab-section-image {
    border-radius: 4px;
}

.av_tab_navigation{
	position: absolute;
}

.avia-section-small .av-layout-tab{
	padding-top: 20px;
	padding-bottom: 20px;
}

.avia-section-large .av-layout-tab{
	padding-top: 70px;
	padding-bottom: 70px;
}

.avia-section-huge .av-layout-tab{
	padding-top: 130px;
	padding-bottom: 130px;
}

.avia-section-no-padding .av-layout-tab{
	padding-top: 0;
	padding-bottom: 0;
}

#top .av-custom-tab-color a{
	color: inherit;
}

.av-tab-no-icon.av-tab-no-image .av-inner-tab-title{
	margin-bottom: 15px;
}

.avia-tab-title-padding-none .av-outer-tab-title{
	padding: 0 0;
    position: relative;
    top: -6px;
}

.avia-tab-title-padding-small .av-outer-tab-title{
	padding: 0 0;
}

.avia-tab-title-padding-default .av-outer-tab-title{
	padding: 10px 0;
}

.avia-tab-title-padding-large .av-outer-tab-title{
	padding: 20px 0;
}

.av-tab-with-icon .av-outer-tab-title{
	margin-bottom: 10px;
	padding: 0 0;
}

.av-tab-without-text .av-outer-tab-title{
	display: none;
}

#top .av-tab-without-text.av-section-tab-title{
	padding-top: 0;
}

#top .av-tab-below-content .av-tab-without-text.av-section-tab-title{
	padding-bottom: 10px;
}

.av-tab-below-content .av-tab-arrow-container{
	position: absolute;
	top: 0;
}

.avia_transform .av-tab-below-content .av-tab-arrow-container span{
	top: -65px;
}

.avia_transform .av-tab-below-content .av-active-tab-title .av-tab-arrow-container span{
	top: -45px;
}

#top .av-tab-below-content .av-section-tab-title{
	padding: 5px 20px 0px 20px;
}

.av-tab-below-content .av-tab-section-tab-title-container{
	padding: 30px 0px 20px 0px;
}

.boxed .av-layout-tab-inner .container,
.html_header_sidebar .av-layout-tab-inner .container{
	margin:0;
}

.av-tab-content-auto .av-layout-tab-inner{
	transition: height 0.4s ease-in-out;
}

/* Arrows for tab header */
.av-tab-above-content .av-tabsection-arrow{
	position: absolute;
	width: 100%;
	top: 30px;
	left: 0;
}

.av-tab-below-content .av-tabsection-arrow{
	position: absolute;
	width: 100%;
	bottom: 30px;
	left: 0;
}

#top .av-tabsection-arrow > a{
	width: 30px;
	margin: -30px 0 0 0;
	display: none;
}

#top .av-tabsection-arrow > a.prev-slide{
	border-radius: 0 5px 5px 0;
}

#top .av-tabsection-arrow > a.next-slide{
	border-radius: 5px 0 0 5px;
}

#top .av-tabsection-arrow.av-visible-prev > a.prev-slide,
#top .av-tabsection-arrow.av-visible-next > a.next-slide{
	display: block;
}

#top .av-tabsection-arrow > a:hover{
	opacity: 0.7;
	transition: all 0.4s ease-in-out;
}

/* ============================================
	Slideshow section specific
=============================================== */
.av-tab-section-container.av-hide-tabs .av-tab-section-tab-title-container,
.av-tab-section-container.av-hide-tabs .av-tabsection-arrow{
	display: none;
}

.av-slideshow-section.av-is-slideshow .av-section-tab-title:hover{
	cursor: default;
}

.av-slideshow-section.av-strech-full .av-layout-tab-inner > .container{
	width: 100%;
	max-width: 100%;
	margin-left: 0;
	margin-right: 0;
	padding-left: 0;
	padding-right: 0;
}

#top .av-tabsection-slides-arrow > a{
	top: 50%;
	width: 0px;
	margin: -30px 0 0 0;
	display: block;
	opacity: 0;
	transition: all 0.8s ease-in-out;
}

#top .av-is-slideshow .av-control-default:not(.av-slideshow-ui) .avia-slideshow-arrows a,
#top .av-is-slideshow .av-control-minimal:not(.av-slideshow-ui) .avia-slideshow-arrows a{
	opacity: 1;
	width: 35px;
}

/*	show/hide nav arrows depending on options and state of slider	*/
#top .av-is-slideshow .av-slideshow-ui.av-loop-endless .avia-slideshow-arrows > a,
#top .av-is-slideshow .av-slideshow-ui.av-loop-manual-endless .avia-slideshow-arrows > a,
#top .av-is-slideshow .av-slideshow-ui .avia-slideshow-arrows.av-visible-prev > a.prev-slide,
#top .av-is-slideshow .av-slideshow-ui .avia-slideshow-arrows.av-visible-next > a.next-slide{
	opacity: 1;
	width: 35px;
	transition: all 0.8s ease-in-out;
}

#top .av-is-slideshow .av-no-slider-navigation .av-tabsection-slides-dots{
	display: none;
}

#top .av-is-slideshow .av-slideshow-ui .av-tabsection-slides-arrow.av-visible-prev > a:hover,
#top .av-is-slideshow .av-slideshow-ui .av-tabsection-slides-arrow.av-visible-next > a:hover{
	opacity: 0.7;
	transition: all 0.4s ease-in-out;
}


/* hide controls on desktop */
.avia_desktop #top .av-slideshow-section.av-is-slideshow .av-slideshow-ui .avia-slideshow-controls a{
	opacity: 0;
}
.avia_desktop #top .av-slideshow-section.av-is-slideshow .av-slideshow-ui.av-nav-arrows-visible .avia-slideshow-arrows a,
.avia_desktop #top .av-slideshow-section.av-is-slideshow .av-slideshow-ui.av-nav-dots-visible .avia-slideshow-dots a{
	opacity: 0.6;
}

.avia_desktop #top .av-slideshow-section.av-is-slideshow:hover .avia-slideshow-controls a{
	opacity: 0.6;
}

.avia_desktop #top .av-slideshow-section.av-is-slideshow:hover .avia-slideshow-controls a:hover{
	opacity: 0.8;
}

#top .av-is-slideshow .av-slideshow-ui.av-hide-nav-arrows .avia-slideshow-arrows a,
#top .av-is-slideshow:hover .av-slideshow-ui.av-hide-nav-arrows .avia-slideshow-arrows a{
	width: 0;
	opacity: 0;
	transition: none;
}

#top .av-tabsection-slides-dots > a{
	transition: all 0.8s ease-in-out;
}

.av-tab-slide-up-transition .av-tab-section-tab-title-container{
	z-index: 2;
}

.av-tab-slide-up-transition .av-slide-section-container-wrap{
	overflow: hidden;
	width: 100%;
}

.av-tab-slide-up-transition .av-tab-section-inner-container,
.av-tab-fade-transition .av-tab-section-inner-container{
	width: 100%;
	display: block;
}

.av-tab-slide-up-transition .av-layout-tab{
	display: block;
	opacity: 0;
	transition: all 0.4s ease-in-out;
}

.av-tab-slide-up-transition.av-tab-content-fixed .av-layout-tab,
.av-tab-fade-transition .av-layout-tab{
	display: flex;
	height: 100%;
	width: 100%;
	flex-direction: column;
    align-items: start;
	justify-content: start;
}

.av-tab-slide-up-transition.av-tab-content-fixed .av-layout-tab.vertical-align-middle,
.av-tab-fade-transition .av-layout-tab.vertical-align-middle{
	justify-content: center;
}

.av-tab-slide-up-transition.av-tab-content-fixed .av-layout-tab.vertical-align-bottom,
.av-tab-fade-transition .av-layout-tab.vertical-align-bottom{
	justify-content: end;
}

.av-tab-slide-up-transition.av-tab-content-fixed .av-layout-tab-inner,
.av-tab-fade-transition .av-layout-tab-inner{
	width: 100%;
}

.av-tab-fade-transition .av-layout-tab.av-active-tab-content{
	position: relative;
	z-index: 5;
	opacity: 1;
}

.av-tab-fade-transition .av-layout-tab{
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
	opacity: 0;
	transition: all 0.8s ease-in-out;
}

/*		slide transitions sidewards and up	*/
.av-tab-slide-transition .av-tab-section-inner-container,
.av-tab-slide-up-transition .av-tab-section-inner-container{
	transition: all 0.4s ease-in-out;
}

.av-tab-slide-up-transition .av-tab-section-outer-container{
	transition: all 0.4s ease-in-out;
}
