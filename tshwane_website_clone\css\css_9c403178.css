/* ======================================================================================================================================================
ICONBOX
====================================================================================================================================================== */
#top .iconbox{
	background: transparent;
}

.iconbox{
	margin-bottom:30px;
	position: relative;
	clear:both;
}

.iconbox_icon{
	float: left;
	margin: 2px 10px 0 0;
	height: 23px;
	width: 23px;
	font-size: 23px;
	line-height: 18px;
	transition: all 0.3s ease-in-out;
}

a.iconbox_icon:hover{
	text-decoration: none;
}

.iconbox_left .iconbox_icon{
	border-radius: 50px;
	text-align: center;
	position: relative;
	top: -7px;
	left: -5px;
	height: 30px;
	width: 30px;
	line-height: 30px;
	margin: 2px 0px 0 0;
}

.iconbox .iconbox_content .iconbox_content_title{
	border: medium none;
	padding: 2px 0 0 0;
	position: relative;
	margin:0 0 16px 0;
	clear:none;
	letter-spacing: 1px;
	text-transform: uppercase;
	font-size:1.25em;
}

#top .iconbox_top{
	margin-top:20px;
	text-align: center;
}

.iconbox_top .iconbox_content{
	padding:45px 20px 20px 20px;
	border-radius: 3px;
	box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.3);
}

.iconbox_top .iconbox_icon{
	float:none;
	position: absolute;
	left: 50%;
	top:-26px;
	margin:0 0 0 -26px;
	padding:15px;
	border-radius:100px;
	box-sizing: content-box;
	font-size: 20px;
	line-height: 24px;
}

.iconbox_left_content .iconbox_content,
.iconbox_right_content .iconbox_content{
	overflow: hidden;
}

.iconbox_left_content .iconbox_content .iconbox_content_title,
.iconbox_right_content .iconbox_content .iconbox_content_title{
	margin: 0 0 -3px 0;
}

#top .iconbox_left_content .iconbox_icon,
#top .iconbox_right_content .iconbox_icon{
	width: 74px;
	height: 74px;
	font-size: 27px;
	line-height: 72px;
	border-style: solid;
	border-width: 1px;
	border-radius: 500px;
	text-align: center;
	margin-right:22px;
}

#top .iconbox_right_content{
	text-align: right;
}

#top .iconbox_right_content .iconbox_icon{
	float: right;
	margin-right:0;
	margin-left:22px;
}

.iconbox .iconbox_content p:last-child{
	margin-bottom: 0;
}

#top .iconbox.av-no-box .iconbox_content{
	padding:0;
	border:none;
	box-shadow:none;
	background: transparent;
}

#top .iconbox.av-no-box .iconbox_icon{
	position: relative;
	top:0;
	margin:0 auto 20px auto;
	left:0;
	width:90px;
	height:90px;
	line-height:90px;
	border-style: solid;
	border-width: 1px;
	padding:0;
	font-size: 30px;
	display:block;
}

#top .iconbox_left_content.av-icon-style-no-border .iconbox_icon,
#top .iconbox_right_content.av-icon-style-no-border .iconbox_icon{
	border:none;
	height:50px;
	width:50px;
	line-height: 50px;
	margin-top: 0;
	font-size: 50px;
}

#top .iconbox_content_container{
	line-height: 1.65em;
	min-height: 5px;	/*  needed when empty content	*/
}

@media only screen and (min-width: 768px) and (max-width: 989px)
{
	#top .iconbox_left_content .iconbox_icon,
	#top .iconbox_right_content .iconbox_icon{
		margin-bottom: 15px;
		/*float: none;*/
		display: inline-block;
	}
}
