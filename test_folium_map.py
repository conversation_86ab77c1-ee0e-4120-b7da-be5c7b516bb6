#!/usr/bin/env python3
"""
Test script for Folium map functionality
"""

import streamlit as st
import folium
from streamlit_folium import st_folium
import pandas as pd
from pathlib import Path

def test_folium_map():
    """Test the Folium map with sample data"""
    st.title("🗺️ Folium Map Test")
    
    # Sample data for testing
    sample_places = [
        {
            'name': 'Union Buildings',
            'type': 'attraction',
            'description': 'The official seat of the South African government',
            'ai_sentiment': 'positive'
        },
        {
            'name': 'Voortrekker Monument',
            'type': 'attraction', 
            'description': 'Heritage monument commemorating the Voortrekkers',
            'ai_sentiment': 'neutral'
        },
        {
            'name': 'Pretoria Zoo',
            'type': 'attraction',
            'description': 'National Zoological Gardens of South Africa',
            'ai_sentiment': 'positive'
        }
    ]
    
    # Create Folium map
    tshwane_center = [-25.7479, 28.1879]
    m = folium.Map(location=tshwane_center, zoom_start=12)
    
    # Add markers
    colors = ['red', 'blue', 'green']
    for i, place in enumerate(sample_places):
        lat = tshwane_center[0] + (i * 0.01)
        lon = tshwane_center[1] + (i * 0.01)
        
        folium.Marker(
            [lat, lon],
            popup=f"<b>{place['name']}</b><br>{place['description']}",
            tooltip=place['name'],
            icon=folium.Icon(color=colors[i % len(colors)])
        ).add_to(m)
    
    # Display map
    st.write("Testing Folium map with sample Tshwane places:")
    map_data = st_folium(m, width=700, height=500)
    
    if map_data['last_object_clicked_popup']:
        st.success(f"✅ Map interaction working! Last clicked: {map_data['last_object_clicked_popup']}")
    
    st.write("Sample places data:")
    st.dataframe(pd.DataFrame(sample_places))

if __name__ == "__main__":
    test_folium_map()
