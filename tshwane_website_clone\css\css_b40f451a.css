/* ======================================================================================================================================================
ICON
====================================================================================================================================================== */

/*right icons*/
#top .av_font_icon a{
	color: inherit;
	display: block;
}

.av_font_icon a:hover, .av_font_icon a:focus {
	text-decoration: none;
}

.av_font_icon {
	display:block;
	text-align: center;
}

.avia-icon-pos-left {
	float:left;
	margin-right: 0.5em;
}

.avia-icon-pos-right {
	float: right;
	margin-left: 0.5em;
}

/*tooltip*/

#top .avia-icon-tooltip{
	width:250px;
	font-size: 15px;
	line-height: 1.6em;
}


/*big icon with border*/
.av_font_icon.av-icon-style-border .av-icon-char{
-webkit-backface-visibility: hidden; /*fix bug in webkit where item shifts a few px on hover*/
}

.av_font_icon.av-icon-style-border .av-icon-char{
	box-sizing: content-box;
	border-radius: 1000px;
	border-width: 3px;
	border-style: solid;
	display: block;
	margin: 0 auto;
	padding:30px;
	color: inherit;
	border-color: inherit;
	position: relative;
	clear: both;
}

.av_icon_caption{
	font-size: 16px;
	text-transform: uppercase;
	line-height: 1.3em;
	display: block;
	text-align: center;
	margin-top:8px;
	font-weight: 400;
}

.av_font_icon.av-icon-style-border .av-icon-char:after {
	pointer-events: none;
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	content: '';
	box-sizing: content-box;
	top: 0;
	left: 0;
	padding: 0;
	z-index: -1;
	box-shadow: 0 0 0 2px rgba(255,255,255,0.1);
	opacity: 0;
	transform: scale(0.9);
	transform-style: preserve-3d;
}

/*	backwards comp. default behaviour prior 4.8.4 with white shadow only	*/
.av_font_icon.av-icon-style-border:not(.avia-sonar-shadow) a.av-icon-char:hover:after {
	animation: sonarEffect 1s ease-out ;
}

/*icon animation*/
.avia_transform .av_font_icon.avia-icon-animate{
	opacity: 0.1;
	transform:scale(0.5);
}

.avia_transform .avia_start_delayed_animation.av_font_icon.avia-icon-animate{
	animation: avia_appear 0.7s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
	transform:scale(1);
}
