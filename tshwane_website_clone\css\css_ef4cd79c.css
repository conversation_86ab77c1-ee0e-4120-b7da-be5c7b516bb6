/* ======================================================================================================================================================
Image
====================================================================================================================================================== */
.avia-image-container{
	display: block;
	position: relative;
	max-width: 100%;
}

.avia-image-container-inner,
.avia_image,
.av-image-caption-overlay{
	border-radius: 3px;
	display: block;
	position: relative;
	max-width: 100%;
}

.avia-image-container.avia-align-center{
	display:block;
	margin: 0 auto 10px auto;
	text-align: center;
	clear: both;
}

.avia-image-container.avia-align-center .avia-image-container-inner{
	margin:0 auto;
	display:inline-block;
	vertical-align:bottom
}

.avia-image-container.avia-align-left{
	display:block;
	float:left;
	margin-right: 15px;
	margin-top: 5px;
}

.avia-image-container.avia-align-right{
	display:block;
	float:right;
	margin-left: 15px;
	margin-top: 5px;
}

.avia-image-container.avia-align-center.avia-builder-el-no-sibling{
	margin-bottom:0;
	margin-top:0;
}

.avia_image + br{
	display: none;
}

.avia-image-overlay-wrap a.avia_image{
	overflow: hidden;
}

.avia-image-overlay-wrap a.avia_image .image-overlay{
	/*transform: scale(1.5);			removed 5.6.10   https://kriesi.at/support/topic/attachment-source-for-av-tab-section-image/#post-1430008  */
}

.avia-image-container .avia_image,
.avia-image-container .avia-image-overlay-wrap{
    transition: all 0.7s;
}

/*	.avia-image-container.av-hover-grow img:hover,			removed 5.0 - doubles scale e.g. 300 -> 363 instead 330 */
.avia-image-container.av-hover-grow .avia-image-overlay-wrap:hover{
    transform: scale(1.1);
}

.avia-image-container.av-hover-grow,
.avia-image-container.av-hover-grow .avia-image-container-inner,
.avia-image-container.av-hover-grow .avia-image-overlay-wrap a.avia_image{
    overflow: visible;
}

/*	@since 5.0	*/
.avia-image-container.av-img-box-shadow .avia-image-overlay-wrap .avia_image,
.avia-image-container.av-img-box-shadow.av-styling-circle .avia-image-container-inner,
.avia-image-container.av-img-box-shadow.av-styling-circle .avia_image{
    overflow: revert;				/*  https://github.com/KriesiMedia/wp-themes/issues/4025  changed from visible -> revert   */
}

/*  https://github.com/KriesiMedia/wp-themes/issues/4025  changed from hidden -> revert
update: added "avia-image-container-inner" selector and changed back to hidden to fix Zoom issue  */
.avia-image-container.av-hover-grow.av-hide-overflow .avia-image-container-inner {
    overflow: hidden;
}

/*styling variations*/
.av-styling-circle .avia-image-container-inner,
.av-styling-circle .avia_image,
.av-styling-circle .av-image-caption-overlay,
.av-styling-circle div.av-caption-image-overlay-bg{
	overflow: hidden;
	border-radius: 10000px;
}

.avia-safari div.av-image-caption-overlay-center{
	-webkit-transform: translate3d(0,0,0); /*flicker fix for caption in safari*/
}

.av-styling-no-styling .avia-image-container-inner,
.av-styling-no-styling .avia_image,
.av-styling-no-styling .av-image-caption-overlay{
	border-radius: 0;
	border:none;
}

/*captions*/
.av-image-caption-overlay{
	position: absolute;
	height:100%;
	width:100%;
	z-index: 10;
	text-align: center;
	transition: all 0.3s ease-in-out;
}


.av-image-caption-overlay-position{
	display: table;
	width: 100%;
	height:100%;
	position: relative;
}

.av-image-caption-overlay-center{
	display:table-cell;
	vertical-align: middle;
	font-size: 1.3em;
	line-height: 1.5em;
	padding: 0px 1.5em;
}

.av-image-caption-overlay-center p:first-child{
	margin-top:0;
}

.av-image-caption-overlay-center p:last-child{
	margin-bottom:0;
}

.av-caption-image-overlay-bg{
	position: absolute;
	height:100%;
	width:100%;
	transition: all 0.3s ease-in-out;
}

.av-overlay-hover-deactivate .avia-image-overlay-wrap:hover .av-caption-image-overlay-bg{
	opacity: 0 !important;
}

.av-overlay-on-hover .av-image-caption-overlay{
	opacity: 0;
}
.av-overlay-on-hover .avia-image-container-inner:hover .av-image-caption-overlay{
	opacity: 1;
}

.avia_transform .avia_animated_image{
	opacity: 0;
}


/* image copyright */
.avia-image-container small.avia-copyright{
	font-size: 0.8em;
	text-align: left;
	line-height: 1.7em;
}
/* Simple */

.avia-image-container .avia-copyright{
	display: block;
}

.avia-image-container.av-has-copyright .avia-image-overlay-wrap{
	position: relative;
	overflow: hidden;
}

.av-styling-circle.avia-image-container.av-has-copyright .avia-image-container-inner{
	overflow: visible;
}

.avia-image-container.av-has-copyright.av-styling-circle .avia-copyright{
	text-align: center;
}

/* Icon Reveal on Hover */
.avia-image-container.av-has-copyright.av-copyright-icon-reveal .avia-copyright{
	position: absolute;
	background-color: rgba(0,0,0,0.1);
	text-align: right;
	color: #fff;
	padding: 0 2em 0 0;
	right: 0;
	bottom: 0;
	width: 2em;
	line-height: 2em;
	max-height: 2em;
	text-indent: -99999px;
	overflow: hidden;
	transition: all 0.3s ease-in;
	z-index: 308;
}

.avia-image-container.av-has-copyright.av-copyright-icon-reveal .avia-copyright:hover{
	width: 100%;
	padding: 0 3em 0 1em;
	text-indent: 0;
	max-height: 100%;
	background-color: rgba(0,0,0,0.4);
}

.avia-image-container.av-has-copyright.av-copyright-icon-reveal .avia-copyright:after{
	content: "\E81e";
	font-family: "entypo-fontello";
	display: block;
	position: absolute;
	right: 0;
	bottom: 0;
	text-indent: 0;
	font-size: 0.8em;
	width: 2.6em;
	height: 2.6em;
	line-height: 2.8em;
	text-align: center;
	color: rgba(255,255,255,0.7);
	border-radius: 3px;
	cursor: pointer;
}

.avia-image-container.av-has-copyright.av-copyright-icon-reveal .avia-copyright:hover:after{
	background-color: rgba(0,0,0,0.2);
	color: rgba(255,255,255,1);
}


.avia-image-container.av-styling-circle.av-has-copyright.av-copyright-icon-reveal .avia-copyright{
	border-radius: 20px;
	background-color: rgba(0,0,0,0.15);
	text-align: right;
}

.avia-image-container.av-styling-circle.av-has-copyright.av-copyright-icon-reveal .avia-copyright:after{
	border-radius: 100%;
}

.avia-image-container.av-styling-circle.av-has-copyright.av-copyright-icon-reveal .avia-copyright:hover{
	border-radius: 15px;
	background-color: rgba(0,0,0,0.5);
}

/*	fade image	*/
.av-hover-fade .avia_image.fade-basic{
	opacity: 1;
}

.av-hover-fade .avia_image.fade-overlay{
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
}

.av-hover-fade .avia-image-container-inner:hover .avia_image.fade-basic{
	animation: avia-fadeOut 0.7s 1 ease-in-out;
	opacity: 0;
}

.av-hover-fade .avia-image-container-inner:hover .avia_image.fade-overlay{
	animation: avia-fadein 0.7s 1 ease-in-out;
	opacity: 1;
}

.avia-image-container.av-hover-fade.av-img-linked:hover{
	cursor: pointer;
}

.avia-image-container.av-hover-fade.av-img-linked:hover a.noHover{
	z-index: 5;		/* @since 5.7   link is not working !!  */
}
