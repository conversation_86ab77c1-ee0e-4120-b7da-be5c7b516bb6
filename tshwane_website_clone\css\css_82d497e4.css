/*
	Fold / Unfold basic stylings for various elements

    Created on : 21.03.2023, 15:59:05
    Author     : <PERSON><PERSON><PERSON>
	@since     : 5.6
*/

.avia-fold-unfold-section{
	position: relative;
	display: block;
	float: left;
	width: 100%;
}

.avia-fold-unfold-section.avia-fold-init{
	max-height: 80px;
	overflow: hidden;
	opacity: 0.3;
}

.avia-fold-textblock-wrap.avia-fold-init:not(.avia-fold-init-done) .avia_textblock,
.avia-fold-section-wrap.avia-fold-init:not(.avia-fold-init-done) + .avia-section,
.avia-fold-grid-row-wrap.avia-fold-init:not(.avia-fold-init-done) + .av-layout-grid-container{
	display: none;
}

.avia-fold-unfold-section.avia-fold-init-done{
	max-height: 500px;
	overflow: hidden;
	opacity: 1;
	transition: all 1.0s ease-in-out;
}

.avia-fold-unfold-section .av-fold-unfold-container{
	position: relative;
	clear: both;
	overflow: hidden;
	max-height: 80px;			/*	set via js for transition, initialize in post css to avoid jumping, adjust to max content height to unfold	*/
	transition: all 0.7s ease-in-out;
}

.avia-fold-unfold-section .av-fold-unfold-container::after{
	opacity: 0;
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient( to bottom, rgba(255,255,255,0), rgba(255,255,255,1) );
	z-index: -1;			/* allow pointer events */
	height: 100%;
	transition: all 0.7s ease-in-out;
}

.avia-fold-unfold-section .av-fold-unfold-container.folded::after{
	z-index: 500;
	opacity: 1;
}

.av-fold-unfold-container .av-fold-unfold-inner{
	display: block;
	position: relative;
	float: left;
	width: 100%;
}

.avia-fold-unfold-section .av-fold-button-wrapper{
	position: relative;
	display: block;
	float: left;
	width: 100%;
	padding-left: 0;
	padding-right: 0;
}

.avia-fold-unfold-section.align-left .av-fold-button-wrapper.av-fold-btn-padding{
	padding-left: 30px;
}

.avia-fold-unfold-section.align-right .av-fold-button-wrapper.av-fold-btn-padding{
	padding-right: 30px;
}

.avia-fold-unfold-section .av-fold-button-container{
	position: relative;
	display: inline-block;
	margin-top: 15px;
	margin-bottom: 15px;
	float: left;
	z-index: 20;
	transition: all 0.7s ease-in-out;
}

.avia-fold-unfold-section.align-right .av-fold-button-container{
	float: right;
}

.avia-fold-unfold-section.align-center .av-fold-button-container{
	left: 50%;
	transform: translateX(-50%);
}

.avia-fold-unfold-section.fold-button .av-fold-button-container{
	padding: 0.5em 0.8em;
	border: 1px solid;
	border-radius: 7px;
}

#top .avia-fold-unfold-section :not(.avia-button-wrap) .av-fold-button-container{
	text-decoration: underline;
}

.avia-fold-unfold-section .av-fold-button-container:hover{
	opacity: 0.6;
	cursor: pointer;
}

/*	Element specific */

.avia-fold-unfold-section .av-fold-unfold-container .avia_textblock{
	display: inline-block;
}
