/**
 * bbPress specific CSS
 *
 * @package bbPress
 * @subpackage Theme
 */

/* =bbPress Style
-------------------------------------------------------------- */

#bbpress-forums hr {
	margin: 0 0 24px 0;
}

#bbpress-forums {
	background: transparent;
	clear: both;
	margin-bottom: 20px;
	overflow: hidden;
	font-size: 13px;
}

#bbpress-forums div.even,
#bbpress-forums ul.even {

}

#bbpress-forums div.odd,
#bbpress-forums ul.odd {

}

#bbpress-forums div.bbp-topic-header,
#bbpress-forums div.bbp-reply-header {

}

#bbpress-forums .status-trash.even,
#bbpress-forums .status-spam.even {

}
#bbpress-forums .status-trash.odd,
#bbpress-forums .status-spam.odd {

}

#bbpress-forums .status-closed,
#bbpress-forums .status-closed a {
	color: inherit;
}

#bbpress-forums ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

#bbpress-forums li {
	list-style: none;
}

#bbpress-forums ul.bbp-lead-topic,
#bbpress-forums ul.bbp-topics,
#bbpress-forums ul.bbp-forums,
#bbpress-forums ul.bbp-replies {
	font-size: 13px;
	overflow: hidden;
	border-width: 1px;
	border-style:solid;
	margin-bottom: 20px;
	clear: both;
}

#bbpress-forums li.bbp-header,
#bbpress-forums li.bbp-body,
#bbpress-forums li.bbp-footer {
	clear: both;
}

#bbpress-forums li.bbp-header,
#bbpress-forums li.bbp-footer {
	border-top-width: 1px;
	border-top-style:solid;
	font-weight: bold;
	padding: 8px;
	text-align: center;
}

#bbpress-forums li.bbp-header {

}

#bbpress-forums li.bbp-header ul {
	overflow: hidden;
}

#bbpress-forums .bbp-forums-list {
	margin: 0 0 0 5px;
	padding-left: 15px;
	border-left-width: 1px;
	border-left-style:solid;
}

#bbpress-forums .bbp-forums-list li {
	display: inline;
	font-size: 11px;
}

#bbpress-forums li.bbp-footer p {
	margin: 0;
	line-height: 1em;
}

#bbpress-forums .bbp-topic-content img,
#bbpress-forums .bbp-reply-content img {
	max-width: 100%;
}

#bbpress-forums .bbp-topic-content ul,
#bbpress-forums .bbp-reply-content ul {
	padding-left: 15px;
}

#bbpress-forums .bbp-topic-content ul li,
#bbpress-forums .bbp-reply-content ul li {
	list-style: square;
}

#bbpress-forums .bbp-forum-content p {
	margin: 5px 0 0;
	font-style: italic;
}

li.bbp-forum-info,
li.bbp-topic-title {
	float: left;
	text-align: left;
	width: 55%;
}
li.bbp-forum-topic-count,
li.bbp-topic-voice-count,
li.bbp-forum-reply-count,
li.bbp-topic-reply-count{
	float: left;
	text-align: center;
	width: 10%;
}

li.bbp-forum-freshness,
li.bbp-topic-freshness{
	text-align: center;
	float: left;
	width: 22%;
}

#bbpress-forums li.bbp-body ul.forum,
#bbpress-forums li.bbp-body ul.topic {
	border-top-width: 1px;
	border-top-style:solid;
	overflow: hidden;
	padding: 8px;
}

li.bbp-header div.bbp-topic-content span#subscription-toggle,
li.bbp-header div.bbp-topic-content span#favorite-toggle,
li.bbp-header div.bbp-reply-content span#subscription-toggle,
li.bbp-header div.bbp-reply-content span#favorite-toggle {
	float: right;
}

#bbpress-forums div.bbp-topic-author,
#bbpress-forums div.bbp-reply-author {
	float: left;
	text-align: center;
	width: 115px;
	margin-right:30px;
}

#bbpress-forums div.bbp-topic-author img.avatar,
#bbpress-forums div.bbp-reply-author img.avatar {
	border: 0;
	max-width: 80px;
	margin: 3px auto 0 auto;
	float: none;
}

#bbpress-forums div.bbp-topic-author a.bbp-author-name,
#bbpress-forums div.bbp-reply-author a.bbp-author-name {
	margin: 0 auto; font-size: 12px; display: inline-block; line-height: 16px;
}

#bbpress-forums div.bbp-topic-author .bbp-author-role,
#bbpress-forums div.bbp-reply-author .bbp-author-role {
	font-size: 11px;
	font-style: italic; line-height: 14px;
}

#bbpress-forums div.bbp-topic-content,
#bbpress-forums div.bbp-reply-content {
	margin-left: 140px;
	padding: 12px 12px 12px 0;
	text-align: left;
}

#bbpress-forums li.bbp-header .bbp-topic-author,
#bbpress-forums li.bbp-footer .bbp-topic-author,
#bbpress-forums li.bbp-header .bbp-reply-author,
#bbpress-forums li.bbp-footer .bbp-reply-author {
	float: left;
	margin: 0;
	padding: 0;
	width: 120px;
}

#bbpress-forums li.bbp-header .bbp-topic-content,
#bbpress-forums li.bbp-footer .bbp-topic-content,
#bbpress-forums li.bbp-header .bbp-reply-content,
#bbpress-forums li.bbp-footer .bbp-reply-content {
	margin-left: 140px;
	padding: 0;
	text-align: left;
}

div.bbp-topic-header,
div.bbp-reply-header,
li.bbp-body div.hentry {
	margin-bottom: 0;
	overflow: hidden;
	padding: 1px 0;
}

div.bbp-topic-header,
div.bbp-reply-header {
	margin-left:107px;
	clear: both;
}

span.bbp-author-ip {
	font-family: 'Helvetica Neue', Arial, Helvetica, 'Nimbus Sans L', sans-serif;
	font-size: 11px;
	font-weight: bold;
}

/* =Breadcrumb and Tags
-------------------------------------------------------------- */

div.bbp-breadcrumb {
	float: left;
}

div.bbp-breadcrumb,
div.bbp-topic-tags {
	font-size: 12px;
}

#bbpress-forums div.bbp-breadcrumb p,
#bbpress-forums div.bbp-topic-tags p {
	margin-bottom: 10px
}

div.bbp-topic-tags {
	margin-top: -20px;
	position: relative;
	float: right;
	top:20px;
}

/* =Admin Links
-------------------------------------------------------------- */

span.bbp-admin-links {
	float: right;
	font-size: 9px;
}
span.bbp-admin-links a {
	font-weight: normal;
	font-size: 10px;
	text-transform: uppercase;
	text-decoration: none;
}
fieldset span.bbp-admin-links {
	float: left;
}
tr td span.bbp-admin-links a:hover {
}

td.bbp-topic-admin-links,
td.bbp-topic-counts {
	width: 50%;
}

.bbp-topic-header a.bbp-topic-permalink,
.bbp-reply-header a.bbp-reply-permalink {
	float: right;
	margin-left: 10px;
}

/* =Toggles
-------------------------------------------------------------- */

.bbp-topic-action #favorite-toggle a {
	text-decoration: none;
	padding: 0 3px 1px;
	border-width: 1px;
	border-style:solid;
	font-weight: bold;
	font-size: 13px;
	border-radius: 8px;
}

.bbp-topic-action #favorite-toggle a:hover {
	/*
	color: #5a5;
	border-color: #7c7;
	background-color: #beb;
	*/
}
.bbp-topic-action #favorite-toggle span.is-favorite a {
	/*
	color: #faa;
	border: 1px solid #faa;
	background-color: #fee;
	*/

	border-width: 1px;
	border-style:solid;
	border-radius: 5px;
}

.bbp-topic-action #favorite-toggle span.is-favorite a:hover {
	/*
	color: #c88;
	border-color: #c88;
	background-color: #fdd;
	*/
}

.bbp-topic-action #subscription-toggle a {
	text-decoration: none;
	padding: 0 3px 1px;
	border-width: 1px;
	border-style:solid;
	font-weight: bold;
	font-size: 13px;
	border-radius: 8px;
	}
.bbp-topic-action #subscription-toggle a:hover {
	/*
		color: #5a5;
		border-color: #7c7;
		background-color: #beb;
	*/
}

.bbp-topic-action #subscription-toggle span.is-subscribed a {
	/*
		color: #faa;
		border: 1px solid #faa;
		background-color: #fee;
	*/
	border-width: 1px;
	border-style:solid;
	border-radius: 5px;
}

.bbp-topic-action #subscription-toggle span.is-subscribed a:hover {
	/*
		color: #c88;
		border-color: #c88;
		background-color: #fdd;
	*/
	border-width: 1px;
	border-style:solid;
}

#bbpress-forums p.bbp-topic-meta {
	margin: 5px 0 5px;
	font-size: 11px;
}

#bbpress-forums p.bbp-topic-meta span {
	white-space: nowrap;
}

/* =Pagination
-------------------------------------------------------------- */

.single-topic .bbp-pagination-count {
	position: relative;
	top:11px;
}

.single-topic .bbp-pagination{
	margin:0;
}

#top .bbp-pagination-links {
	float: left;
	margin:0 0 10px 0;
}

#top .bbp-pagination-links > span,
#top .bbp-pagination-links > a {
	font-size: 11px;
	line-height: 10px;
	height: 30px;
	padding: 9px 9px;
	border-width: 1px;
	border-style: solid;
	text-decoration: none;
	border-radius: 3px;
	min-width: 30px;
	display: inline-block;
	text-align: center;
}

#top .bbp-pagination {
	position: relative;
	clear:both;
}

#top .bbp-topic-pagination {
	display: inline-block;
	margin-left: 5px;
	margin-bottom: 2px;
	}

#top .bbp-topic-pagination a {
	font-size: 11px;
	line-height: 10px;
	height: 20px;
	padding: 4px 1px;
	border-width: 1px;
	border-style: solid;
	text-decoration: none;
	border-radius: 2px;
	min-width: 20px;
	display: inline-block;
	text-align: center;
}

.bbp-pagination-links span.current{
	font-weight: bold;
}

/* =Forms
-------------------------------------------------------------- */

#bbpress-forums fieldset.bbp-form {
	clear: left;
}

#bbpress-forums fieldset.bbp-form {
	border-width: 1px;
	border-style:solid;
	padding: 10px 20px;
	margin-bottom: 10px;
}

#bbpress-forums fieldset.bbp-form legend {
	padding: 15px 0 4px 0;
}

#bbpress-forums fieldset.bbp-form label {
	margin: 0;
	display: inline-block;
}

#bbp-edit-topic-tag.bbp-form fieldset.bbp-form label,
#bbp-login fieldset label,
#bbp-register fieldset label,
#bbp-lost-pass fieldset label {
	width: 100px;
}

#bbpress-forums fieldset.bbp-form p{
	margin: 0 0 8px;
}

#bbpress-forums fieldset.bbp-form textarea,
#bbpress-forums fieldset.bbp-form select,
#bbpress-forums fieldset.bbp-form input[type=text] {
	margin: 0;
	width: 100%;
	display: inline;
	min-width: 100px;
	padding: 13px;
	border-radius: 2px;
}

textarea#bbp_reply_content,
textarea#bbp_topic_content,
textarea#bbp_forum_content {
	width: 97%;
	box-sizing: border-box;
}

textarea#bbp_forum_content {
	height: 210px;
}

#bbpress-forums fieldset.bbp-forum-form-attributes {
	width: 200px;
	float: right;
	clear: none;
	margin-left: 25px;
}

.bbp-topic-form,
.bbp-reply-form,
.bbp-topic-tag-form {
	clear: left;
}

body.topic-edit .bbp-topic-form div.avatar img,
body.reply-edit .bbp-reply-form div.avatar img,
body.single-forum .bbp-topic-form div.avatar img,
body.single-reply .bbp-reply-form div.avatar img {
	margin-right: 0;
	padding: 10px;
	border-width: 1px;
	border-style:solid;
	line-height: 0;
}

body.page .bbp-reply-form code,
body.page .bbp-topic-form code,
body.single-topic .bbp-reply-form code,
body.single-forum .bbp-topic-form code,
body.topic-edit .bbp-topic-form code,
body.reply-edit .bbp-reply-form code {
	font-size: 10px;
	border-width: 1px;
	border-style:solid;
	display: block;
	padding: 8px;
	margin-top: 5px;
	width: 369px;
}

#merge_tag,
#delete_tag {
	display: inline;
}

div.bbp-submit-wrapper {
	padding-top: 8px;
	clear: both;
}

p.form-allowed-tags {
	width: 462px;
}

#bbpress-forums>#subscription-toggle{
	float: right;
	font-size: 12px;
}

/* =TinyMCE in themes
-------------------------------------------------------------- */
#bbpress-forums div.bbp-the-content-wrapper {
	margin-bottom: 10px;
}

#bbpress-forums div.bbp-the-content-wrapper textarea.bbp-the-content {
	width: 100%;
	margin: 0;
	font-size: 13px;
	line-height: 1.8em;
}

#bbpress-forums div.bbp-the-content-wrapper table,
#bbpress-forums div.bbp-the-content-wrapper tbody,
#bbpress-forums div.bbp-the-content-wrapper tr,
#bbpress-forums div.bbp-the-content-wrapper td {
	border: none;
	padding: 0;
	margin: 0;
	width: auto;
	line-height: 1em;
}

#bbpress-forums div.bbp-the-content-wrapper input {
	font-size: 12px;
	padding: 5px;
	margin: 3px 0 0;
	line-height: 1em;
	margin: 0;
}

#bbpress-forums div.bbp-the-content-wrapper div.quicktags-toolbar {
	padding: 5px;
	min-height: 26px;
}
#bbpress-forums div.bbp-the-content-wrapper td.mceToolbar {
	padding: 4px 4px 8px;
}

#bbpress-forums div.wp-editor-container {
	margin: 0;
	padding: 0;
	line-height: 0;
}

#bbpress-forums div.bbp-the-content-wrapper td.mceStatusbar {
	line-height: 16px;
}

/* =Edit User
-------------------------------------------------------------- */
#bbp-your-profile{
	padding-top:20px;
}

#bbp-your-profile h2{
	font-size:20px;
	margin-bottom:0;
}

#bbp-your-profile fieldset {
	margin-top: 3px;
	padding: 20px 20px 0 20px;
}
#bbp-your-profile fieldset div {
	margin-bottom: 20px;
	float: left;
	width: 100%;
	clear: left;
}

#bbp-your-profile fieldset select {
	margin-bottom: 0;
}

#bbp-your-profile fieldset input,
#bbp-your-profile fieldset textarea,
#bbp-your-profile fieldset select {
	margin-bottom: 0;
	width: 50%;
	min-width:0;
}

#bbp-your-profile fieldset select{
width:51.5%;
}

#bbp-your-profile fieldset legend {
	display: none;
}

.content #bbp-your-profile fieldset label,
#container #bbp-your-profile fieldset label {
	float: left;
	width: 150px;
	padding-right: 20px;
	text-align: left;
}

#bbp-your-profile fieldset span.description {
	margin: 5px 0 0 170px;
	font-size: 12px;
	font-style: italic;
	float: left;
	clear: left;
	width: 48.5%;
	padding: 5px 10px;
	border-width: 1px;
	border-style: solid;
	border-style: solid;
}

.content #bbp-your-profile fieldset fieldset,
#container #bbp-your-profile fieldset fieldset {
	margin: 0;
	border: none;
	padding: 0;
	clear: none;
	overflow: hidden;
	float:none;
	width: auto;
}

.content #bbp-your-profile fieldset fieldset input{
	width: 68%;
}

.content #bbp-your-profile fieldset  fieldset span.description {
	width:65.5%;
}

#bbp-your-profile fieldset fieldset span.description {
	margin-left: 0;
	margin-bottom: 20px;
}

#bbp-your-profile fieldset.submit button {
	float: right;
}

/* =Notices
-------------------------------------------------------------- */

div.bbp-template-notice {
	border-width: 1px;
	border-style: solid;
	padding: 0 0.6em;
	margin: 5px 0 15px;
	border-radius: 3px;
	background-color: #ffffe0;
	border-color: #e6db55;
	color: #000;
	clear: both;
}

div.bbp-template-notice a {
	color: #555;
	text-decoration: none;
}
div.bbp-template-notice a:hover {
	color: #000;
}

div.bbp-template-notice.info {
	border: #cee1ef 1px solid;
	background-color: #f0f8ff;
}

div.bbp-template-notice.important {
	border: #e6db55 1px solid;
	background-color: #fffbcc;
}

div.bbp-template-notice.error,
div.bbp-template-notice.warning {
	background-color: #ffebe8;
	border-color: #c00;
}

div.bbp-template-notice.error a,
div.bbp-template-notice.warning a {
	color: #c00;
}

div.bbp-template-notice p {
	margin: 0.5em 0 6px 0 !important;
	padding: 2px;
	font-size: 12px;
	line-height: 140%;
}

/* =Stickies
-------------------------------------------------------------- */

.bbp-topics-front ul.super-sticky,
.bbp-topics ul.super-sticky,
.bbp-topics ul.sticky,
.bbp-forum-content ul.sticky {
	background-color: #ffffe0;
	font-size: 1.1em;
}

/* =Revisions
-------------------------------------------------------------- */
#bbpress-forums .bbp-topic-revision-log,
#bbpress-forums .bbp-reply-revision-log {
	border-top: 1px dotted #ddd;
	list-style: none;
	width: 100%;
	margin: 0;
	padding: 8px 0 0 0;
	font-size: 11px;
	color: #aaa;
}

/* =Widgets
-------------------------------------------------------------- */

#top .bbp-login-form fieldset legend {
	display: none;
}

#top .bbp_widget_login{
	text-align: left;
}

#top .bbp_widget_login .button{
	border-radius: 2px;
}

#top .bbp_widget_login .bbp-logged-in{
	position: relative;
}

#top .bbp_widget_login .bbp-logged-in .button {
	position: absolute;
	top: -4px;
	left: 2%;
	max-width: 98%;
	min-width: 50%;
	padding: 14px;
	width: 98%;
	text-align: center;
	line-height: 1em;
	margin: 4px 2px 0 0;
	float: left;
	font-size: 12px;
}

#top .widget fieldset {
	max-width: 100%;
	width: 100%;
}

#top .bbp-login-form .bbp-username input,
#top .bbp-login-form .bbp-email input,
#top .bbp-login-form .bbp-password input {
	padding: 10px;
	border-radius: 2px;
	margin-bottom: 7px;
}

#top .bbp-login-form label {
	width: 140px;
	display: inline-block;
}

#sidebar .bbp-login-form label {
	width: 70px;
}

.bbp-login-form .bbp-username,
.bbp-login-form .bbp-email,
.bbp-login-form .bbp-password,
.bbp-login-form .bbp-remember-me,
.bbp-login-form .bbp-submit-wrapper {
	margin-top: 10px;
}

.bbp-login-form .bbp-remember-me {

}

.bbp-login-form .bbp-submit-wrapper {
	text-align: left;
}

.bbp-login-form .bbp-login-links {
	overflow: hidden;
	padding: 2px 0;
}

.bbp-login-form .bbp-login-links a {
	display:block;
	margin-right:5px;
	line-height: 1em;
	font-size: 11px;
	padding: 3px 0;
}

.bbp-register-link{

}


.bbp-logged-in img.avatar {
	margin-right: 15px;
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
	position: relative;
	z-index: 100;
}

.bbp_widget_login .bbp-logged-in h4 {
	font-weight: 600;
	font-size: 1.3em;
	clear: none;
	margin-bottom: 10px;
	font-size: 12px;
	display: none;
}

#top .bbp_widget_login .bbp-submit-wrapper{
	margin:2px 10px 0 0 ;
	padding:0;
	float:left;
}



/* =Avatars
-------------------------------------------------------------- */

#bbpress-forums p.bbp-topic-meta img.avatar,
#bbpress-forums ul.bbp-reply-revision-log img.avatar,
#bbpress-forums ul.bbp-topic-revision-log img.avatar,
#bbpress-forums div.bbp-template-notice img.avatar,
#bbpress-forums .widget_display_topics img.avatar,
#bbpress-forums .widget_display_replies img.avatar,
#bbpress-forums p.bbp-topic-meta img.avatar {
	float: none;
	margin-bottom: -7px;
	border-width: 1px;
	border-style: solid;
}

fieldset div.avatar {
	float: right;
}

/* =BuddyPress Activity Streams
-------------------------------------------------------------- */

.activity-list li.bbp_topic_create .activity-content .activity-inner,
.activity-list li.bbp_reply_create .activity-content .activity-inner {
	border-left-width: 2px;
	border-left-style:solid;
	margin-left: 5px;
	padding-left: 10px;
}

/* =User Pages
-------------------------------------------------------------- */

#bbpress-forums h1 {
	clear: none;
	font-size: 1.8em;
	line-height: 1em;
	padding-bottom: 10px;
}

#bbpress-forums h2.entry-title {
	font-size: 1.4em;
	margin-bottom: 0;
	padding-bottom: 10px;
	padding-top: 0;
}

#bbpress-forums #entry-author-info {
	margin: 10px 0 20px 0;
	overflow: hidden;
}

#bbpress-forums #entry-author-info #author-avatar {
	float: left;
	margin-right: 0;
	width: 60px;
}

#bbpress-forums #entry-author-info #author-avatar img.avatar {
	max-width: 60px;
}

#bbpress-forums #entry-author-info #author-description {
	float: none;
	margin-left: 100px;
}

#bbp-author-subscriptions,
#bbp-author-favorites,
#bbp-author-topics-started {
	border-top-width: 1px;
	border-top-style:solid;
	clear: both;
	margin-bottom: 20px;
	padding-top: 20px;
}

body.my-account #bbpress-forums,
body.my-account #bbp-author-subscriptions,
body.my-account #bbp-author-favorites,
body.my-account #bbp-author-topics-started {
	border-top: none;
	padding-top: 0;
	margin-bottom: 0;
}

/* =BuddyPress Group Forums
-------------------------------------------------------------- */

#bbpress-forums div.row-actions {
	font-size: 11px;
	visibility: hidden;
}

#bbpress-forums li:hover > div.row-actions {
	visibility: visible;
}


/* =theme stuff
-------------------------------------------------------------- */

.bbp-topic-started-by img{
	display:none;
}

#top table .bbp-forum-title, #top table .bbp-topic-title a {
	font-weight:bold;
}

.edit_user_link, .page-title.author{
	display:none;
}

.logged-in .edit_user_link, .logged-in .page-title.author{
	display:inline;
}

#top #bbp_stick_topic{
	width:221px;
	min-width:221px;
}

#top .bbp-admin-links a{
	font-size:10px;
}

#top #main .bbp-template-notice p{
	margin:10px 0;
}

#top #main .bbp-template-notice{
	padding:10px 16px;
}

#top #main .bbp-template-notice,
#top #main .bbp-template-notice a
{
/*	border:1px solid #4b87d0; */
	color:#fff;
	background: #a6bfde;
	border:none;
	border-radius: 0;
}

#top #main .bbp-template-notice.error,
#top #main .bbp-template-notice.error a
{
/* 	border:1px solid #941210;  */
	color:#941210;
	background: #f0dcdb;
	border:none;
	border-radius: 0;
}

#top #main .bbp-template-notice.error,
#top #main .bbp-template-notice.error a
{
/* 	border:1px solid #E6DB55;  */
	color:#9d5501;
	background: #FFFFE0;
	border:none;
	border-radius: 0;
}

#top .avia-box.tick{
	border-color:#8BAF5B;
	background: #E0F1B5;
	color:#4F5F39;
}

td.bbp-topic-title{
	padding-right:50px;
	background-position: 95% center;
	background-repeat: no-repeat;
}

.sticky td.bbp-topic-title{
	background-image: url("images/sticky.png");
}

.super-sticky td.bbp-topic-title{
	background-image: url("images/super-sticky.png");
}

.status-closed td.bbp-topic-title{
	background-image: url("images/closed.png");
}


/*2.1 changes*/

#bbpress-forums li{
	margin-left:0px;
}

#bbpress-forums .bbp-header .bbp-forum-info,
#bbpress-forums .bbp-header .bbp-topic-title{
	padding-left:12px;
}

div #bbpress-forums li.bbp-header{
	border-top:none;
	margin-left:0;
}

.single-topic.logged-in #bbpress-forums li.bbp-header{
	padding-bottom:30px;
}

#bbpress-forums li.bbp-header ul li{
	margin-bottom:0;
	font-weight: bold;
}

.bbp-forum-freshness, .bbp-topic-freshness{
	display:none;
}

.bbp-forum-info, .bbp-topic-title,
#bbpress-forums p.bbp-topic-meta {
	margin:0px;
}

.bbp-forum-title,
.bbp-topic-permalink,
.bbp-author-name{
	font-weight: bold;
}

#bbpress-forums li.bbp-body{
	padding:0;
}

#bbpress-forums li.bbp-header,
#bbpress-forums li.bbp-footer {
	padding: 10px 0;
}

#bbpress-forums li.bbp-body ul.forum,
#bbpress-forums li.bbp-body ul.topic {
	padding: 6px 12px;
}

li.bbp-forum-info, li.bbp-topic-title {
	width: 78%;
}

.bbp-pagination-count {
	float: right;
	font-size: 11px;
	border: none;
	display: block;
	padding: 0;
}

.bbp-reply-header .bbp-meta{
	font-size:11px;
}

.bbp-body .bbp-reply-author{
	border:none;
}

li.bbp-body div.hentry {
	padding: 0 0 30px 0;
}

#bbpress-forums .bbp-body div.bbp-topic-content,
#bbpress-forums .bbp-body div.bbp-reply-content {
	min-height: 68px;
	text-align: left;
	overflow: hidden;
	border-radius: 2px;
	padding:7px 20px;
	border-style:solid;
	border-width:6px;
	position: relative;
	margin-left:0;
	margin-right:0;
}

#bbpress-forums div.bbp-topic-author,
#bbpress-forums div.bbp-reply-author {
	width: auto;
}

#bbpress-forums div.bbp-topic-author img.avatar,
#bbpress-forums div.bbp-reply-author img.avatar {
	max-width: 60px;
	border-radius: 100px;
}

#bbpress-forums .bbp-reply-content li{
	margin-bottom:0;
	padding: 0;
}

#bbpress-forums .bbp-reply-content ol,
#bbpress-forums .bbp-reply-content ul{
	margin-bottom:20px;
}

#bbpress-forums fieldset.bbp-form {
	margin: 10px 0% 10px 0%;
	width: 100%;
	padding:0;
	border:none;
}

#bbpress-forums li.bbp-header .bbp-topic-author,
#bbpress-forums li.bbp-footer .bbp-topic-author,
#bbpress-forums li.bbp-header .bbp-reply-author,
#bbpress-forums li.bbp-footer .bbp-reply-author {
	width: 67px;
}

#bbpress-forums li.bbp-header .bbp-topic-content,
#bbpress-forums li.bbp-footer .bbp-topic-content,
#bbpress-forums li.bbp-header .bbp-reply-content,
#bbpress-forums li.bbp-footer .bbp-reply-content {
	margin-left: 90px;
}

.bbp-reply-content blockquote p{
	margin: 0.5em 0;
}


.bbp-topic-action #favorite-toggle span.is-favorite a,
.bbp-topic-action #subscription-toggle span.is-subscribed a {
	border-top-width: 1px;
	border-width: 1px;
	border-style: solid;
	border-radius:0;
	padding: 0;
	line-height: 19px;
	height: 20px;
	width: 20px;
	display: block;
	float: left;
	text-align: center;
	position: absolute;
	right: -1px;
	top: -1px;
}

.bbp-body ul{
	position: relative;
}



/*added with bbpress 2.2.3*/

#bbp-single-user-details{
	border-style:solid;
	border-width:1px;
	padding: 0 0 10px;
	margin-bottom:30px;
}

#bbp-single-user-details #bbp-user-avatar{
	float:left;
	margin: 0 10px;
	width:32px;
}

#bbp-single-user-details #bbp-user-navigation{
	overflow:hidden;
	margin: 0 10px;
}

#bbp-single-user-details #bbp-user-navigation li{
	float:left;
	margin:0px 5px;
	line-height: 27px;
	width:100%;
}

.bbp-forums,
.bbp-topics,
.bbp-replies{
	border-radius: 3px;
}

.bbp-footer{
	display:none;
}

#top .mceToolbar{
	background: transparent;
}

.bbp-form a{
	box-sizing: content-box;
}

#bbpress-forums ul.bbp-replies{
	border:none;
}

.bbp-header .bbp-reply-author{
	display: none;
}

.bbp-header .bbp-reply-author,
.bbp-header .bbp-reply-content{
	visibility: hidden;
}

.bbp-header .bbp-reply-content span{
	visibility: visible;
}

.bbp-reply-post-date{
	font-size: 11px;
}

.bbp-body > div{
	position: relative;
}

.avia_transform .bbp-replies .bbp-reply-author::before,
.avia_transform .forum-search .bbp-reply-author::before,
.avia_transform .forum-search .bbp-topic-author::before
{
	width: 14px;
	height: 14px;
	transform: rotate(45deg);
	position: absolute;
	top: 25px;
	z-index: 100;
	content: "";
	left: 103px;
	border-left-style: solid;
	border-bottom-style: solid;
	border-left-width: 1px;
	border-bottom-width: 1px;
}

#top .bbp-replies+.bbp-pagination{
	display:none;
}

#top .bbp-replies .bbp-header{
	border-bottom-style: solid;
	border-bottom-width: 1px;
	margin-bottom:30px;
}


#top #bbp_search {
	float: left;
	margin-right: 5px;
	top: 1px;
	position: relative;
}

.single-topic #bbpress-forums{
	margin-top:-30px;
}

.single-topic #bbpress-forums div.bbp-reply-author,
.forum-search #bbpress-forums div.bbp-reply-author,
.bbp-user-page #bbpress-forums div.bbp-reply-author,
.forum-search #bbpress-forums div.bbp-topic-author{
	width:75px;
}

.status-trash{
	opacity: 0.3;
}

#footer .bbp-forum-title{
	font-weight: normal;
}

.widget_display_topics li div{
	font-size: 11px;
	font-style: italic;
}

#bbpress-forums div.bbp-search-form{
	float:none;
}

#top #bbp-search-form #bbp_search{
	width:75%;
	margin:0;
	padding: 12px 12px;
}

#top #bbp-search-form #bbp_search_submit{
	width:25%;
	padding: 14px 22px;
	top:-1px;
	position: relative;
}

#bbpress-forums .bbp-forum-info .bbp-forum-content,
#bbpress-forums p.bbp-topic-meta{
	font-size: 12px;
}

/*stats widget*/

.widget_display_stats dt{
	width:65%;
	padding:4px 10px;
	margin:0px 1px 1px 0;
}

.widget_display_stats dd{
	width:34%;
	padding:4px;
	margin:0 0 1px 0;
	text-align: center;
}

.bbp-search-author {
	width: 105px;
	float: left;
	text-align: left;
}

.bbp-search-content{
	float:left;
}

.bbp-reply-revision-log-item a img,
.bbp-topic-revision-log-item a img{
	position: relative;
	top: -4px;
	border-radius: 10px;
}

/*2.5 mods*/
#top .bbp-body .page-numbers.dots,
#bbpress-forums div.odd,
#bbpress-forums div.bbp-forum-header,
#bbpress-forums div.bbp-topic-header,
#bbpress-forums div.bbp-reply-header,
#bbpress-forums li.bbp-header{
	background: transparent;
}

#bbpress-forums div.bbp-topic-header,
#bbpress-forums div.bbp-reply-header{
	border:none;}

#bbpress-forums div.bbp-topic-content a,
#bbpress-forums div.bbp-reply-content a,
#subscription-toggle a{
	font-weight: bold;
}

#bbp-single-user-details #bbp-user-navigation li {
	width: auto;
}

#bbp-single-user-details {
	overflow: hidden;
	padding: 10px;
}

#bbp-single-user-details #bbp-user-avatar{
	margin:0 10px 0 0;
}

#bbp-single-user-details #bbp-user-avatar img{
	display:block;
}


/*topic status default iconfont values. overwriten in case user applies a custom font value*/
.bbp-topics .bbp-body .bbp-topic-title:before{
	content:"\E83b";
	font-family: 'entypo-fontello';
	font-size: 14px;
	font-weight: normal;
	height:20px;
	width:20px;
	line-height:20px;
	text-align: center;
	margin-right:4px;
}

.bbp-topics .bbp-body .topic-voices-multi .bbp-topic-title:before{
	content:"\E83c";
}

.bbp-topics .bbp-body .super-sticky .bbp-topic-title:before{
	content:"\E808";
}

.bbp-topics .bbp-body .sticky .bbp-topic-title:before{
	content:"\E809";
}

.bbp-topics .bbp-body .status-closed .bbp-topic-title:before{
	content:"\E824";
}

.bbp-topics .bbp-body .super-sticky.status-closed .bbp-topic-title:before{
	content:"\E808 \E824";
	letter-spacing: 3px;
}

.bbp-topics .bbp-body .sticky.status-closed .bbp-topic-title:before{
	content:"\E809 \E824";
	letter-spacing: 3px;
}

#bbpress-forums  .wp-core-ui .button{
	background: #FAFAFA;
	border-color: #bbb;
	color: #555;
	margin: 1px 1px 1px 0;
	min-width: 34px;
	transition: 		none;
}

#bbpress-forums  .wp-core-ui .button:hover{
	background: #fff;
	border-color: #999;
}

.bbp-topics-front ul.super-sticky,
.bbp-topics ul.super-sticky,
.bbp-topics ul.sticky,
.bbp-forum-content ul.sticky {
	background-color: #F2FBFF !important;
}

#top #wrap_all .bbp-body span.page-numbers.dots {
	background: rgba(0, 0, 0, 0);
}


/* #Media Queries
================================================== */

/*mobile*/
@media only screen and (max-width: 767px) {

	.responsive li.bbp-forum-info,
	.responsive li.bbp-topic-title {
		width: 60%;
	}

	.responsive li.bbp-forum-topic-count,
	.responsive li.bbp-topic-voice-count,
	.responsive li.bbp-forum-reply-count,
	.responsive li.bbp-topic-reply-count {
		width: 18%;
	}

	.responsive #top #bbp-your-profile .bbp-form label{
		float:none;
		text-align: left;
	}

	.responsive #bbp-your-profile fieldset input,
	.responsive #bbp-your-profile fieldset textarea,
	.responsive #bbp-your-profile fieldset select {
		width: 95%;
	}

	.responsive #bbp-your-profile fieldset span.description,
	.responsive .content #bbp-your-profile fieldset fieldset span.description {
		width: 91%;
		margin:5px 0 15px 0;
	}

	.responsive .content #bbp-your-profile fieldset fieldset input {
		width: 95%;
	}

	.responsive .bbp-pagination-count{
		display:none;
	}

}


/* #some basic BuddyPress styling code
================================================== */
/* general */
#buddypress .standard-form div.submit input,
#buddypress #aw-whats-new-submit {
    padding: 8px;
}

#top #buddypress #item-header-avatar{
    max-width: 150px;
    float: left;
    margin-right: 30px;
    margin-bottom: 30px;
}

#top #main .group-create.bp-title-button{
    background: transparent;
    border: none;
    padding-left: 20px;
    font-weight: bold;
}

#top #main .group-create.bp-title-button:hover{
    text-decoration: underline;
}

/* activity */
#buddypress form#whats-new-form #whats-new-options select,
#buddypress div.item-list-tabs ul li.last select,
#top #activity-filter-select label {
    display: inline;
}

#top #buddypress #whats-new-options{
    min-height: 70px;
    overflow: visible ;
    max-width: 98%;
    margin-bottom: 20px;
}

#buddypress div.item-list-tabs ul li a,
#buddypress div.item-list-tabs ul li span{
    font-weight: bold;
}

/* groups */
#top #buddypress div.dir-search input[type=text],
#top #buddypress li.groups-members-search input[type=text]{
    padding: 6px;
    font-size: 100%;
}

#top #buddypress #group-dir-search input,
#top #buddypress #group-dir-search label,
#top #buddypress #groups-order-select input,
#top #buddypress #groups-order-select label{
    display: inline;
}

#top #buddypress #group-dir-search #groups_search_submit{
    padding: 8px;
}

/* members */
#top #buddypress #members-dir-search input,
#top #buddypress #members-dir-search label,
#top #buddypress #members-order-select input,
#top #buddypress #members-order-select label{
    display: inline;
}

#top #buddypress #members-dir-search #members_search_submit{
    padding: 8px;
}

/* messages */
#top #buddypress .message-search input,
#top #buddypress .message-search label{
    display: inline;
}

#top #buddypress .message-search #messages_search_submit{
    padding: 8px;
}

/* responsive styles */
@media only screen and (max-width: 479px) {

    .responsive #top #buddypress form#whats-new-form #whats-new-submit{
        display: block;
        float: none;
        margin-bottom: 10px;
    }

    .responsive #top #buddypress form#whats-new-form #whats-new-options select {
        max-width: 140px;
    }

}
