/* ======================================================================================================================================================
Video
====================================================================================================================================================== */

.avia-video,
.avia-iframe-wrap{
	clear:both;
	position: relative;
	margin-bottom:20px;
}

.avia-video,
.avia-video iframe,
.avia-video video{
	background-color: #000;
}

/*responsive iframe trick*/
.avia-video iframe,
.js_active .avia-iframe-wrap iframe,
div .avia-video .avia-iframe-wrap{
	position: absolute;
	width:100%;
	height:100%;
	left:0;
	top:0;
	padding:0;
}
.avia-video-custom{
	height:0;
}

.avia-video-16-9,
.js_active .avia-iframe-wrap{
	padding-bottom: 56.25%;
	height:0;
}

.avia-video-4-3{
	padding-bottom: 75%;
	height:0;
}

/*html 5 video - always has 100 width and natural height. behaves like image when it comes to aspect ratio*/
video{
	width: 100%;
	height: auto;
}

.mejs-layer{
    z-index: 1;
    width: 100% !important;
}

.mejs-layer.mejs-overlay-play{
	height: 100% !important;
}

#top .avia-video .mejs-container,
#top .avia-video .mejs-container video{
	height: 100% !important;
	width: 100% !important;
	position: absolute;
}

#top .avia-video .av-click-to-play-overlay{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	cursor: pointer;
	z-index: 1000;
}

#top .avia-video .av-click-to-play-overlay .avia_playpause_icon{
	display: block;
	transition: opacity 0.4s ease-out;
}

#top .avia-video .av-click-to-play-overlay:hover .avia_playpause_icon{
	opacity: 0.7;
}

.avia-video .mejs-poster,
.avia-video{
	background-position: center center;
	background-size: cover;
}

.avia-video .mejs-poster{
	position: absolute;
	height: 100% !important;
	width: 100% !important;
	top: 0;
	left: 0;
}
