(function($)
{
	"use strict";

	// -------------------------------------------------------------------------------------------
	// makes sure that the fixed container height is removed once the layerslider is loaded, so it adapts to the screen resolution
	// -------------------------------------------------------------------------------------------

	$.fn.layer_slider_height_helper = function(options)
	{
		return this.each(function()
		{
			var container 	= $(this),
				first_div 	= container.find('>div').first(),
				timeout 	= false,
				counter 	= 0,
				reset_size 	= function()
				{
					if(first_div.height() > 0 || counter > 5)
					{
						container.height('auto');
					}
					else
					{
						timeout = setTimeout(reset_size, 500);
						counter++;
					}
				};

			if( ! first_div.length )
			{
				return;
			}

			timeout = setTimeout(reset_size, 0);
		});
	};


}(jQuery));