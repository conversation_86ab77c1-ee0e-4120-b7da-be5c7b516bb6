Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9CF0, 0007FFFF8BF0) msys-2.0.dll+0x1FEBA
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210285FF9, 0007FFFF9BA8, 0007FFFF9CF0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9CF0  0002100690B4 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9FD0  00021006A49D (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81E7C0000 ntdll.dll
7FF81D8A0000 KERNEL32.DLL
7FF81C170000 KERNELBASE.dll
7FF81D270000 USER32.dll
7FF81C140000 win32u.dll
7FF81D750000 GDI32.dll
7FF81BFE0000 ucrtbase.dll
7FF81BC80000 gdi32full.dll
7FF81BA40000 msvcp_win.dll
7FF81CB80000 sechost.dll
000210040000 msys-2.0.dll
7FF81D790000 advapi32.dll
7FF81D5B0000 msvcrt.dll
7FF81DA90000 RPCRT4.dll
7FF81AF80000 CRYPTBASE.DLL
7FF81BBE0000 bcryptPrimitives.dll
7FF81DA50000 IMM32.DLL
7FF80AFC0000 windhawk.dll
7FF813C70000 WINHTTP.dll
7FF80AF10000 explorer-details-better-file-sizes_1.4.7_976364.dll
7FF81C860000 ole32.dll
7FF81E370000 combase.dll
7FF81D970000 OLEAUT32.dll
7FF81DC60000 SHELL32.dll
7FF815D50000 PROPSYS.dll
7FF80AD70000 libc++.dll
7FF80AD30000 libunwind.dll
7FFFFB900000 slick-window-arrangement_1.0.2_652869.dll
7FF80D790000 COMCTL32.dll
7FF8190D0000 dwmapi.dll
7FF81D660000 shcore.dll
