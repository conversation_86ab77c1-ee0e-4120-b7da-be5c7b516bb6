Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAC00, 0007FFFF9B00) msys-2.0.dll+0x1FEBA
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210285FF9, 0007FFFFAAB8, 0007FFFFAC00, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAC00  0002100690B4 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEE0  00021006A49D (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF907DE0000 ntdll.dll
7FF9061B0000 KERNEL32.DLL
7FF905410000 KERNELBASE.dll
7FF9064E0000 USER32.dll
7FF9058D0000 win32u.dll
7FF9066B0000 GDI32.dll
7FF905060000 ucrtbase.dll
7FF905A70000 gdi32full.dll
7FF9059C0000 msvcp_win.dll
7FF905ED0000 sechost.dll
000210040000 msys-2.0.dll
7FF907150000 advapi32.dll
7FF906090000 msvcrt.dll
7FF906F30000 RPCRT4.dll
7FF9045A0000 CRYPTBASE.DLL
7FF905370000 bcryptPrimitives.dll
7FF905D30000 IMM32.DLL
7FF8F1980000 windhawk.dll
7FF8FCDF0000 WINHTTP.dll
7FF8ECED0000 explorer-details-better-file-sizes_1.4.7_976364.dll
7FF905B90000 ole32.dll
7FF907210000 combase.dll
7FF905D70000 OLEAUT32.dll
7FF907600000 SHELL32.dll
7FF8FFF60000 PROPSYS.dll
7FF8F5840000 libc++.dll
7FF8F59E0000 libunwind.dll
7FF8F5810000 slick-window-arrangement_1.0.2_652869.dll
7FF8F6610000 COMCTL32.dll
7FF902710000 dwmapi.dll
7FF9063F0000 shcore.dll
