/* ======================================================================================================================================================
Image with Hotspots
====================================================================================================================================================== */


.av-hotspot-container {
	position: relative;
	display: table;
	margin: 0 auto;
	clear: both;
	table-layout:fixed
}

.av-hotspot-container-inner-cell{
	display: table-cell;
}

.av-fullwidth-hotspots .av-hotspot-container-inner-cell{
	width:100%;
	display:block;
}

.av-hotspot-container-inner-wrap img{
	width:100%;
	display: block;
}

.av-hotspot-container .av-image-hotspot{
	height:18px;
	width:18px;
	font-size: 10px;
	line-height: 18px;
	text-align: center;
	position: absolute;
	z-index: 1;
	margin:-9px 0 0 -9px;
}

.av-hotspot-container .av-image-hotspot_inner{
	display:block;
	border-radius: 100px;
	position: absolute;
	height:100%;
	width:100%;
	background: #fff;
	color: #888;
	z-index: 2;
	cursor: pointer;
	text-decoration: none;
}

.av-hotspot-container .av-image-hotspot_inner:hover{
	background: #fff;
	color: #888;
}

.av-hotspot-blank .av-hotspot-container .av-image-hotspot_inner{
	overflow: hidden;
	text-indent: -200%;
}

.av-hotspot-container .av-image-hotspot-pulse {
	display: block;
	background: #fff;
	border-radius: 100px;
	height: 40px;
	width: 40px;
	position: absolute;
	animation: av_pulsate 2s linear infinite;
	opacity: 0;
	top: -11px;
	left: -11px;
	z-index: 1;
}

.av-tt-hotspot{
	text-align: left;
}

.av-hotspot-fallback-tooltip{
	padding:15px 15px 0 15px;
	position: relative;
	display:none;
}

.av-hotspot-image-container .av-hotspot-fallback-tooltip:last-child{
	padding-bottom:15px;
}

.av-hotspot-fallback-tooltip-count{
	height:40px;
	width:40px;
	line-height: 40px;
	border-style: solid;
	border-width: 1px;
	text-align: center;
	border-radius: 100px;
	position: absolute;
	top:15px;
	left:15px;
	z-index: 10;
}

.av-hotspot-fallback-tooltip-inner{
	margin-left:60px;
	border: 1px solid;
	padding: 15px;
	position: relative;
	border-radius: 2px;
}

.av-hotspot-fallback-tooltip-inner img{
	max-width:100px;
	margin:0 10px 0 0;
	display:block;
}

.av-hotspot-fallback-tooltip-inner p:first-child, .inner_tooltip p:first-child{
	margin-top:0;
}

.av-hotspot-fallback-tooltip-inner p:last-child, .inner_tooltip p:last-child{
	margin-bottom:0;
}

.av-hotspot-fallback-tooltip div.avia-arrow {
	left: 60px;
	top: 13px;
	border-right: none;
	border-top: none;
}

body .av-tt-large-width{
	width:250px;
}

body .av-tt-xlarge-width{
	width:350px;
}

/*non-fullwidth hotspot*/
.av-non-fullwidth-hotspot-image .av-hotspot-fallback-tooltip{
	padding-left:0;
	padding-right: 0;
}

.av-non-fullwidth-hotspot-image .av-hotspot-fallback-tooltip-count{
	left:0px;
}

/*fullwidth hotspot*/
.av-fullwidth-hotspots{
	border-top:none;
}

.av-fullwidth-hotspots img{
	width:100%;
	border-radius: 0px;
}

.av-fullwidth-hotspots .av-hotspot-container{
	display: block;
}

/*image and hotspot appearing*/
.avia_transform.js_active .avia_animated_image .av-image-hotspot{
	opacity:0;
	visibility: hidden;
}

.avia_transform.js_active .avia_animated_image .av-display-hotspot{
	animation: avia_hotspot_appear 0.7s 1 cubic-bezier(0.175, 0.885, 0.320, 1.275);
	opacity: 1;
	visibility: visible;
}

.avia_transform.js_active .avia_animated_image .av-image-hotspot .av-permanent-tooltip-single{
	opacity: 0 !important;
	transition: all 0.7s cubic-bezier(0.230, 1.000, 0.320, 1.000) 1s;
	transform: translate(0px,10px);
}

.avia_transform.js_active .avia_animated_image .av-display-hotspot .av-permanent-tooltip-single{
	opacity: 1 !important;
	transform:translate(0px,0);
}


@media only screen and (min-width: 990px)
{
	.responsive  .av-image-hotspot{
		height:24px;
		width:24px;
		line-height: 24px;
		font-size: 11px;
	}

	.responsive  .av-image-hotspot-pulse{
		height: 54px;
		width: 54px;
		top: -15px;
		left: -15px;
	}
}

@media only screen and (max-width: 767px)
{
	.responsive .av-hotspot-fallback-tooltip{
		display:block;
	}

	.responsive .av-mobile-fallback-active.avia-tooltip{
		visibility: hidden;
	}

	.responsive .av-hotspot-image-container.av-hotspot-blank.av-mobile-fallback-active .av-image-hotspot_inner{
		text-indent: 0px;
	}
}
