#!/usr/bin/env python3
"""
Test script to verify the first container displays content properly
"""

import streamlit as st
import pandas as pd
import folium
from streamlit_folium import st_folium

def create_sample_data():
    """Create sample tourism data for testing"""
    return [
        {
            'name': 'Union Buildings',
            'display_name': 'Union Buildings',
            'type': 'attraction',
            'description': 'The official seat of the South African government',
            'ai_sentiment': 'positive',
            'ai_categories': ['government', 'historical'],
            'verified_source': True
        },
        {
            'name': 'Voortrekker Monument',
            'display_name': 'Voortrekker Monument',
            'type': 'attraction',
            'description': 'Heritage monument commemorating the Voortrekkers',
            'ai_sentiment': 'neutral',
            'ai_categories': ['heritage', 'monument'],
            'verified_source': True
        },
        {
            'name': 'Pretoria Zoo',
            'display_name': 'National Zoological Gardens',
            'type': 'attraction',
            'description': 'National Zoological Gardens of South Africa',
            'ai_sentiment': 'positive',
            'ai_categories': ['nature', 'family'],
            'verified_source': True
        }
    ]

def test_container_display():
    """Test that the first container always displays content"""
    st.title("🧪 Container Display Test")
    
    # Simulate session state
    if 'places_data' not in st.session_state:
        st.session_state.places_data = create_sample_data()
    if 'restaurants_data' not in st.session_state:
        st.session_state.restaurants_data = []
    
    # Test the main container content
    st.subheader("🗺️ Main Container Content Test")
    
    total_places = len(st.session_state.places_data)
    total_restaurants = len(st.session_state.restaurants_data)
    
    # Always show content
    if total_places > 0 or total_restaurants > 0:
        st.success(f"📊 Loaded {total_places} places and {total_restaurants} restaurants")
        
        # Test Streamlit native map as fallback
        st.info("📍 Testing Streamlit native map display...")
        
        if st.session_state.places_data:
            map_data = []
            for i, place in enumerate(st.session_state.places_data):
                lat = -25.7479 + (i * 0.005)
                lon = 28.1879 + (i * 0.005)
                map_data.append({
                    'lat': lat,
                    'lon': lon,
                    'name': place.get('display_name', place.get('name', 'Unknown')),
                    'type': place.get('type', 'attraction')
                })
            
            if map_data:
                df = pd.DataFrame(map_data)
                st.map(df, zoom=12, use_container_width=True)
                st.caption(f"📍 Showing {len(map_data)} places on map")
        
        # Test data display
        with st.expander("📍 Data Display Test", expanded=True):
            tab1, tab2 = st.tabs(["🏛️ Places", "📊 Summary"])
            
            with tab1:
                if st.session_state.places_data:
                    places_df = pd.DataFrame([
                        {
                            'Name': place.get('display_name', place.get('name', 'Unknown')),
                            'Type': place.get('type', 'Unknown'),
                            'Sentiment': place.get('ai_sentiment', 'neutral'),
                            'Categories': ', '.join(place.get('ai_categories', [])),
                            'Verified': '✅' if place.get('verified_source') else '⚠️'
                        }
                        for place in st.session_state.places_data
                    ])
                    st.dataframe(places_df, use_container_width=True)
                else:
                    st.info("No places data available")
            
            with tab2:
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Total Places", total_places)
                with col2:
                    st.metric("Total Restaurants", total_restaurants)
                with col3:
                    st.metric("Total Items", total_places + total_restaurants)
    else:
        # Test empty state display
        st.info("🗺️ Welcome to Tshwane Tourism Portal")
        st.markdown("""
        **To get started:**
        1. Load tourism data
        2. Explore places on the map
        3. View detailed information
        """)
    
    st.success("✅ Container display test completed - content should always be visible!")

if __name__ == "__main__":
    st.set_page_config(page_title="Container Test", layout="wide")
    test_container_display()
