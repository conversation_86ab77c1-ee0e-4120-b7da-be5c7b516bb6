/* ======================================================================================================================================================
GOOGLE MAPS
====================================================================================================================================================== */

.avia-google-maps-section{
	border:none;
}

.avia-google-maps-section.avia-builder-el-0{
	border-top-style: solid;
	border-top-width: 1px;
}

.avia-google-map-container{
	position: relative;
	clear:both;
}

.avia-google-map-container img {
	max-width: none;
}

.avia-google-map-container div,
.avia-google-map-container img,
.avia-google-map-container a{
	box-sizing: content-box;
}

.avia-google-map-container .gm-style-iw p {
	color: #444;
}

.av_gmaps_sc_main_wrap .avia-google-map-container.avia-google-map-sc{
	background: no-repeat center;
	background-size: cover;
}

.av_gmaps_sc_main_wrap .av_text_confirm_link{
	padding: 25px;
	text-align: center;
	opacity: 0;
	position: absolute;
	width: 150px;
	left: 50%;
	top: 50%;
	margin-left: -75px;
	margin-top: -50px;
	background: rgba(0,0,0,0.7);
	border-radius: 3px;
	color: #fff;
	font-size: 1em;
	line-height: 1.3em;
	transition: all 0.3s ease-in-out;
	text-decoration: none;
}

.av_gmaps_sc_main_wrap .av_text_confirm_link:hover{
	color: #fff;
    background: rgba(0,0,0,0.9);
}

.av_gmaps_sc_main_wrap .av_gmaps_show_delayed.av-no-fallback-img .av_text_confirm_link,
.av_gmaps_sc_main_wrap .av_gmaps_show_page_only.av-no-fallback-img .av_text_confirm_link{
	height: auto;
	opacity: 1;
}

.av_gmaps_sc_main_wrap .av_text_confirm_link span{
	width: 100%;
}

.av_gmaps_sc_main_wrap:hover .av_text_confirm_link{
	opacity: 1;
	text-decoration: none;
}

#top .av_gmaps_browser_disabled, #top .av-maps-user-disabled .av_text_confirm_link{
	display: none;
}

#top .av-maps-user-disabled .av_gmaps_browser_disabled{
	display: block;
}

/* fix in Google Maps version > 3.45 scrolling to map on page load */
#top .gm-ui-hover-effect{
    display: none !important;
}
#top .gm-ui-hover-effect.avia-show-gm-notice{
    display: block !important;
}

