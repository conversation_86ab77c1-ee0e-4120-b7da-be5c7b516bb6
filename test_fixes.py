#!/usr/bin/env python3
"""
Test script to verify the fixes for Folium map and nested columns
"""

import streamlit as st
import folium
from streamlit_folium import st_folium


def test_folium_attribution():
    """Test Folium map with proper attributions"""
    st.title("🗺️ Folium Attribution Test")

    # Create map with proper attributions
    m = folium.Map(location=[-25.7479, 28.1879], zoom_start=12)

    # Add tile layers with attributions
    folium.TileLayer(
        tiles='Stamen Terrain',
        attr='Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors',
        name='Terrain'
    ).add_to(m)

    folium.TileLayer(
        tiles='CartoDB positron',
        attr='© CartoDB © OpenStreetMap contributors',
        name='Light'
    ).add_to(m)

    # Add layer control
    folium.LayerControl().add_to(m)

    # Add a test marker
    folium.Marker(
        [-25.7479, 28.1879],
        popup="Test Marker",
        tooltip="Tshwane Center"
    ).add_to(m)

    # Display map - test responsive sizing
    st.write("Testing Folium map with proper attributions and responsive sizing:")
    try:
        map_data = st_folium(
            m,
            width=None,  # Use None to fill container width
            height=600,  # Set appropriate height
            use_container_width=True  # Fill the container width
        )
        st.success(
            "✅ Folium map displayed successfully with attributions and responsive sizing!")
        st.info("Map should fill the width of its container")
        return True
    except Exception as e:
        st.error(f"❌ Folium map error: {e}")
        return False


def test_no_nested_columns():
    """Test that we don't have nested columns"""
    st.title("📊 Column Nesting Test")

    # Create main columns
    col1, col2 = st.columns([2, 1])

    with col1:
        st.write("Main content area")
        st.metric("Test Metric 1", 42)

    with col2:
        st.write("Sidebar content")
        # Test analytics without nested columns
        st.metric("📍 Places", 5)
        st.metric("🍽️ Restaurants", 3)
        st.metric("🔔 Notifications", 2)

    st.success("✅ No nested columns - layout works correctly!")


def main():
    st.set_page_config(page_title="Fix Tests", layout="wide")

    st.header("🔧 Testing Fixes")

    # Test 1: Folium attribution
    if test_folium_attribution():
        st.success("✅ Fix 1: Folium attribution - PASSED")
    else:
        st.error("❌ Fix 1: Folium attribution - FAILED")

    st.divider()

    # Test 2: No nested columns
    try:
        test_no_nested_columns()
        st.success("✅ Fix 2: No nested columns - PASSED")
    except Exception as e:
        st.error(f"❌ Fix 2: No nested columns - FAILED: {e}")


if __name__ == "__main__":
    main()
