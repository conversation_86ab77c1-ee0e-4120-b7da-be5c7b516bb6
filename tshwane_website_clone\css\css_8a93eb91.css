/* ======================================================================================================================================================
#Comment
====================================================================================================================================================== */


h4#comments{
	margin-bottom:30px;
}

#respond{
	margin-top:20px;
}

.commentlist ul {
	border-left-style: dashed;
	border-left-width: 1px;
}

.children .children .says{
	border-bottom-style: dashed;
	border-bottom-width: 1px;
}

.miniheading,
.author_name,
#reply-title,
#top .logged-in-as,
.dynamic-column-title{
	font-weight: 600;
	letter-spacing: 1px;
}

#comments span,
.minitext,
.form-allowed-tags,
#reply-title small,
#commentform label{
	font-size: 0.85em;
	display:block;
	letter-spacing: 0;
	text-transform: none;
	padding-top:8px;
	line-height: 1.5em;
	font-weight: normal;
}

.comment_meta_container{
	clear:both;
	float:none;
}

#top .commentlist{
	margin:0;
	padding: 0 0 10px 0px;
	border:none;
}

#top .commentlist .comment{
	list-style-type: none;
	list-style-position: outside;
	width:100%;
	position: relative;
	display: block;
	background: none;
	min-height:100px;
	clear: both;
}

#top .commentlist .comment.depth-1{
	float:left;
}

#top .commentlist .comment>div{
	min-height: 100px;
	float: left;
	width:100%;
}

.commentlist>.comment{
	border-bottom-style: dashed;
	border-bottom-width: 1px;
	margin-bottom:30px;
}

.gravatar{
	position: relative;
	z-index: 2;
	border-radius: 200px;
	overflow: hidden;
	float: left;
}

.gravatar img{
	padding:0;
	margin:0;
	display: block;
	border-radius: 200px;
}

.comment_content{
	position:relative;
	margin:0 0 0 85px;
	padding:0 35px 15px 0;
	z-index: 10;
	overflow: hidden;
}

.author_name a,
.author_name a:hover{
	font-weight: bold;
	text-decoration: none;
}

.comment-edit-link,
#cancel-comment-reply-link{
	display:inline-block;
	font-size: 10px;
}

.author_name,
.comment_title{
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
}

.commentmetadata {
	font-size:11px;
	line-height: 1em;
}

.commentmetadata a{
	text-decoration: none;
}

.commentmetadata a:hover{
	text-decoration: underline;
}

.says {
	display: block;
	height: 1px;
	left: -70px;
	position: absolute;
	text-indent: 200%;
	top: 18px;
	width: 40px;
	overflow: hidden;
}

#top .comment_text{
	clear: both;
	font-size: 13px;
	padding:  0 0 15px 0;
	border:none;
}

#top .comment-reply-link {
	font-size: 11px;
	text-decoration: none;
	line-height: 20px;
}

.side-container-comment-inner{
	text-align: center;
	position: relative;
	overflow: hidden;
	margin-bottom:15px;
}

.comment-count {
	font-size: 24px;
	line-height: 60px;
	width: 60px;
	display: block;
	text-align: center;
	border-radius: 200px;
	margin: 0 auto;
	position: relative;
	z-index: 100;
}

.side-container-comment-inner .comment-text{
	font-size: 12px;
	text-transform: uppercase;
}

.center-border{
	position: absolute;
	top:39%;
	width:42%;
	border-top-style: solid;
	border-top-width: 1px;
	z-index: 1;
}
.center-border-left{
	left:0;
}

.center-border-right{
	right:0;
}


/*children*/
#top .commentlist ul{
	margin: 0 0 0 74px;
	clear:both;
}

#top .commentlist .children ul{
	margin: 0 0 0 47px;
}

.children .comment_content {
	margin: 0 0 0 28px;
	padding-bottom:30px;
	z-index: 1;
}

.children .gravatar{
	position: relative;
	left:-24px;
	z-index: 2;
	width:45px;
}

#top .children .comment-reply-link {
	left: -42px;
	top: 51px;
}

/*comment page nav*/

.comment_page_nav_links{
	position: relative;
	display: block;
	clear:both;
	overflow: hidden;
	font-size:11px;
}

.comment_prev_page a{
	float:left;
}

.comment_next_page a{
	float:right;
}

.comment_page_nav_links_bottom{
}

.comment_page_nav_links_top{

}

.sidebar_right .comment_container {
	padding-right: 50px;
}

.sidebar_left .comment_container {
	margin-left: 0;
}

.comment_container{
	max-width: 100%;
}

/*generated by comment_form()*/

#commentform{
	position: relative;
}

#commentform p{
	position: relative;
	padding:0 0 10px 0;
	margin:0;
}

#reply-title small a{
	float:right;
}

#commentform label{
	position: absolute;
	left:245px;
	font-size: 11px;
	top:0;
	font-weight: bold;
}

#commentform input[type='checkbox'] + label{
	left:2.5em;
}

#commentform div input{
	margin:0;
}

.commentlist #commentform label{
	position: static;
	display: block;
}

.comment-notes, #commentform  .comment-form-comment label{
	display:none;
}

#top .comment-form-url input,
#top .comment-form-email input,
#top .comment-form-author input{
	width: 220px;
}

#top .commentlist .comment-form-url input,
#top .commentlist .comment-form-email input,
#top .commentlist .comment-form-author input{
	width: 70%;
}

#comment{
	width:602px;
	height:150px;
	padding:10px 7px;
	font-size: 12px;
	margin:0;
}

.form-allowed-tags{
	font-size: 11px;
	line-height: 1.5em;
	margin-bottom: 5px;
}

.form-allowed-tags code{
	display:block;
	padding-top:5px;
}

.commentlist #respond {
	padding: 0 0 30px 56px;
}

.commentlist #respond #comment{
	width:90%;
}

.commentlist #respond .form-allowed-tags{
	display:none;
}

#reply-title{
	display:none;
}

#reply-title small{
	display:inline;
}

.commentlist #reply-title{
	display:block;
}

#comment{
	width:94%;
	font-size:12px;
}

.personal_data p{
	float:left;
	width:33%;
}

.personal_data label{
	display:none;
}

.template-blog .post .entry-content-wrapper{
	/*
		font-size: 14px;
		line-height: 1.7em;
	*/
	overflow:hidden;
}

.template-blog .post_delimiter{
	margin: 0 0 50px 0;
	padding: 50px 0 0 0;
	/*
		border-bottom-style: solid;
		border-bottom-width: 1px;
	*/
	clear:both;
}

.template-blog .post-entry-last .post_delimiter{
	border:none;
	height:1px;
}

.av-buildercomment .av-buildercomment-unapproved{
	padding: 30px 0 15px;
	text-align: center;
	font-size: 1.4em;
	font-weight: 500;
}